# 环境变量配置说明

## 📁 配置文件位置

### 主要配置文件
- **`.env`** - 主要环境变量配置文件（项目根目录）
- **`.env.example`** - 配置文件模板（项目根目录）

### 相关脚本
- **`scripts/load_env.sh`** - 环境变量加载脚本
- **`scripts/switch_model.sh`** - 模型快速切换脚本

## 🔧 配置文件说明

### .env 文件结构
```bash
# 服务器配置
SERVER_PORT=8080
GIN_MODE=debug

# 数据库配置
DB_HOST=***********
DB_PORT=3380
DB_USERNAME=gmdns
DB_PASSWORD=Suyan15913..
DB_DATABASE=solve_web

# AI模型配置
MODEL_OCR=qwen-vl-plus          # OCR模型
MODEL_SOLVE=qwen-plus           # 解题模型 (关键配置)
```

## 🚀 模型切换方法

### 方法1: 使用快速切换脚本（推荐）

```bash
# 查看当前模型配置
./scripts/switch_model.sh status

# 切换到qwen模型
./scripts/switch_model.sh qwen

# 切换到deepseek模型
./scripts/switch_model.sh deepseek

# 重启服务使配置生效
./start.sh
```

### 方法2: 直接修改.env文件

1. 编辑 `.env` 文件：
```bash
# 使用qwen模型
MODEL_SOLVE=qwen-plus

# 或使用deepseek模型
MODEL_SOLVE=deepseek-chat
```

2. 重启服务：
```bash
./start.sh
```

### 方法3: 临时环境变量（仅当次有效）

```bash
# 临时切换到deepseek
export MODEL_SOLVE=deepseek-chat
./start.sh

# 临时切换到qwen
export MODEL_SOLVE=qwen-plus
./start.sh
```

## 📋 支持的模型

### OCR模型
- **qwen-vl-plus** - 通义千问视觉模型（默认）

### 解题模型
- **qwen-plus** - 通义千问Plus模型（默认）
- **deepseek-chat** - DeepSeek对话模型

## ✅ 验证配置

### 检查当前配置
```bash
# 查看模型配置状态
./scripts/switch_model.sh status

# 验证deepseek模型配置
go run scripts/verify_deepseek_config.go

# 测试deepseek模型集成
go run scripts/test_deepseek_integration.go
```

### 检查服务状态
```bash
# 查看服务状态
./run.sh status

# 查看服务日志
./run.sh logs

# 健康检查
curl http://localhost:8080/health
```

## 🔍 故障排除

### 常见问题

1. **环境变量未生效**
   - 确保修改了 `.env` 文件
   - 重启服务：`./start.sh`
   - 检查配置：`./scripts/switch_model.sh status`

2. **deepseek模型调用失败**
   - 检查数据库中是否有deepseek模型配置
   - 验证API Key是否正确设置
   - 运行验证脚本：`go run scripts/verify_deepseek_config.go`

3. **模型切换无效**
   - 确保重启了服务
   - 检查日志：`tail -f logs/service.log`
   - 验证环境变量：`echo $MODEL_SOLVE`

### 日志检查
```bash
# 查看模型调用日志
grep "模型:" logs/service.log

# 查看deepseek相关日志
grep -i "deepseek" logs/service.log

# 查看请求体日志
grep "HTTP-REQUEST" logs/service.log
```

## 📝 配置示例

### 开发环境配置
```bash
# .env
SERVER_PORT=8080
GIN_MODE=debug
MODEL_SOLVE=qwen-plus
LOG_LEVEL=debug
```

### 生产环境配置
```bash
# .env
SERVER_PORT=8080
GIN_MODE=release
MODEL_SOLVE=deepseek-chat
LOG_LEVEL=info
```

## 🛠️ 高级配置

### 自定义模型参数
模型的详细参数在数据库 `hook_models` 表中配置：
- `temperature` - 温度参数
- `top_p` - TopP参数
- `repetition_penalty` - 重复惩罚（映射为deepseek的frequency_penalty）
- `presence_penalty` - 存在惩罚

### 添加新模型
1. 在数据库中添加模型配置
2. 修改 `internal/services/model_service.go` 添加模型支持
3. 更新环境变量配置

## 📞 技术支持

如果遇到配置问题，请：
1. 查看日志文件：`logs/service.log`
2. 运行验证脚本检查配置
3. 检查数据库中的模型配置记录
4. 确保API Key正确设置
