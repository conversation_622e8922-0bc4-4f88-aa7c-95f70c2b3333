# Solve Go API 环境变量配置文件示例
# 复制此文件为 .env 并根据实际情况修改配置值

# ===========================================
# 服务器配置
# ===========================================
SERVER_PORT=8080
GIN_MODE=debug

# ===========================================
# 数据库配置 (MySQL)
# ===========================================
DB_HOST=localhost
DB_PORT=3306
DB_USERNAME=your_username
DB_PASSWORD=your_password
DB_DATABASE=solve_web
DB_CHARSET=utf8mb4

# ===========================================
# Redis配置
# ===========================================
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_PASSWORD=your_redis_password
REDIS_DB=0

# ===========================================
# 短信服务配置 (阿里云)
# ===========================================
SMS_ACCESS_KEY_ID=your_access_key_id
SMS_ACCESS_KEY_SECRET=your_access_key_secret
SMS_SIGN_NAME=your_sign_name
SMS_TEMPLATE_CODE=your_template_code

# ===========================================
# AI模型配置
# ===========================================
# OCR模型选择 (qwen-vl-plus)
MODEL_OCR=qwen-vl-plus

# 解题模型选择 (qwen-plus 或 deepseek-chat)
# 要使用deepseek模型，请设置为: deepseek-chat
# 要使用qwen模型，请设置为: qwen-plus
MODEL_SOLVE=qwen-plus

# ===========================================
# JWT配置
# ===========================================
JWT_SECRET=your-secret-key-here
JWT_EXPIRE_HOURS=24

# ===========================================
# 其他配置
# ===========================================
# 日志级别 (debug, info, warn, error)
LOG_LEVEL=debug

# 文件上传配置
UPLOAD_MAX_SIZE=10485760
UPLOAD_ALLOWED_TYPES=jpg,jpeg,png,gif

# API限流配置
RATE_LIMIT_REQUESTS=100
RATE_LIMIT_WINDOW=60

# ===========================================
# 模型切换说明
# ===========================================
# 1. 使用qwen-plus模型（默认）:
#    MODEL_SOLVE=qwen-plus
#
# 2. 使用deepseek模型:
#    MODEL_SOLVE=deepseek-chat
#
# 注意：
# - 修改模型配置后需要重启服务
# - 确保数据库中有对应模型的配置记录
# - deepseek模型需要设置正确的API Key
