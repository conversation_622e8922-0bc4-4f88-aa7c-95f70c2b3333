2025/06/18 12:43:17.700007 main.go:37: 📝 [SYSTEM] 日志系统初始化完成，日志将同时输出到终端和文件: logs/service.log
2025/06/18 12:43:17.700666 main.go:46: Warning: .env file not found: unexpected character "-" in variable name near "curl -X GET \"http://localhost:8080/api/v1/manager/logs/?page=1&page_size=20\" \\\n  -H \"Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1c2VyX2lkIjoxLCJyb2xlIjoiYWRtaW4iLCJpc3MiOiJzb2x2ZS1nby1hcGkiLCJleHAiOjE3NTAyMzQ3MDIsIm5iZiI6MTc1MDE0ODMwMiwiaWF0IjoxNzUwMTQ4MzAyfQ.h9MDbNktI4QLbyoXJ7WVaBzTAGwlSiCP3NRDBDfMnoE\"\n\n# 获取单个日志\ncurl -X GET http://localhost:8080/api/v1/manager/logs/{log_id} \\\n  -H \"Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1c2VyX2lkIjoxLCJyb2xlIjoiYWRtaW4iLCJpc3MiOiJzb2x2ZS1nby1hcGkiLCJleHAiOjE3NTAyMzQ3MDIsIm5iZiI6MTc1MDE0ODMwMiwiaWF0IjoxNzUwMTQ4MzAyfQ.h9MDbNktI4QLbyoXJ7WVaBzTAGwlSiCP3NRDBDfMnoE\"\n\n# 删除日志\ncurl -X DELETE http://localhost:8080/api/v1/manager/logs/{log_id} \\\n  -H \"Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1c2VyX2lkIjoxLCJyb2xlIjoiYWRtaW4iLCJpc3MiOiJzb2x2ZS1nby1hcGkiLCJleHAiOjE3NTAyMzQ3MDIsIm5iZiI6MTc1MDE0ODMwMiwiaWF0IjoxNzUwMTQ4MzAyfQ.h9MDbNktI4QLbyoXJ7WVaBzTAGwlSiCP3NRDBDfMnoE\"\n\n# 获取日志统计\ncurl -X GET http://localhost:8080/api/v1/manager/logs/stats \\\n  -H \"Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1c2VyX2lkIjoxLCJyb2xlIjoiYWRtaW4iLCJpc3MiOiJzb2x2ZS1nby1hcGkiLCJleHAiOjE3NTAyMzQ3MDIsIm5iZiI6MTc1MDE0ODMwMiwiaWF0IjoxNzUwMTQ4MzAyfQ.h9MDbNktI4QLbyoXJ7WVaBzTAGwlSiCP3NRDBDfMnoE\"\n\n# 8. 超级管理员接口 (需要admin权限)\n\n# 获取所有用户列表 (支持过滤)\ncurl -X GET \"http://localhost:8080/api/v1/admin/users/?page=1&page_size=20&role=user&is_active=1\" \\\n  -H \"Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1c2VyX2lkIjoxLCJyb2xlIjoiYWRtaW4iLCJpc3MiOiJzb2x2ZS1nby1hcGkiLCJleHAiOjE3NTAyMzQ3MDIsIm5iZiI6MTc1MDE0ODMwMiwiaWF0IjoxNzUwMTQ4MzAyfQ.h9MDbNktI4QLbyoXJ7WVaBzTAGwlSiCP3NRDBDfMnoE\"\n\n# 获取单个用户详情\ncurl -X GET http://localhost:8080/api/v1/admin/users/{user_id} \\\n  -H \"Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1c2VyX2lkIjoxLCJyb2xlIjoiYWRtaW4iLCJpc3MiOiJzb2x2ZS1nby1hcGkiLCJleHAiOjE3NTAyMzQ3MDIsIm5iZiI6MTc1MDE0ODMwMiwiaWF0IjoxNzUwMTQ4MzAyfQ.h9MDbNktI4QLbyoXJ7WVaBzTAGwlSiCP3NRDBDfMnoE\"\n\n# 更新用户信息\ncurl -X PUT http://localhost:8080/api/v1/admin/users/{user_id} \\\n  -H \"Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1c2VyX2lkIjoxLCJyb2xlIjoiYWRtaW4iLCJpc3MiOiJzb2x2ZS1nby1hcGkiLCJleHAiOjE3NTAyMzQ3MDIsIm5iZiI6MTc1MDE0ODMwMiwiaWF0IjoxNzUwMTQ4MzAyfQ.h9MDbNktI4QLbyoXJ7WVaBzTAGwlSiCP3NRDBDfMnoE\" \\\n  -H \"Content-Type: application/json\" \\\n  -d '{\n    \"nickname\": \"新昵称\",\n    \"is_active\": 1,\n    \"balance\": 5000\n  }'\n\n# 获取用户统计信息\ncurl -X GET http://localhost:8080/api/v1/admin/users/stats \\\n  -H \"Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1c2VyX2lkIjoxLCJyb2xlIjoiYWRtaW4iLCJpc3MiOiJzb2x2ZS1nby1hcGkiLCJleHAiOjE3NTAyMzQ3MDIsIm5iZiI6MTc1MDE0ODMwMiwiaWF0IjoxNzUwMTQ4MzAyfQ.h9MDbNktI4QLbyoXJ7WVaBzTAGwlSiCP3NRDBDfMnoE\"\n\n# 应用管理 (开发中)\ncurl -X GET http://localhost:8080/api/v1/admin/apps/ \\\n  -H \"Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1c2VyX2lkIjoxLCJyb2xlIjoiYWRtaW4iLCJpc3MiOiJzb2x2ZS1nby1hcGkiLCJleHAiOjE3NTAyMzQ3MDIsIm5iZiI6MTc1MDE0ODMwMiwiaWF0IjoxNzUwMTQ4MzAyfQ.h9MDbNktI4QLbyoXJ7WVaBzTAGwlSiCP3NRDBDfMnoE\"\n\n# 系统配置管理 (开发中)\ncurl -X GET http://localhost:8080/api/v1/admin/config/ \\\n  -H \"Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1c2VyX2lkIjoxLCJyb2xlIjoiYWRtaW4iLCJpc3MiOiJzb2x2ZS1nby1hcGkiLCJleHAiOjE3NTAyMzQ3MDIsIm5iZiI6MTc1MDE0ODMwMiwiaWF0IjoxNzUwMTQ4MzAyfQ.h9MDbNktI4QLbyoXJ7WVaBzTAGwlSiCP3NRDBDfMnoE\"\n\n# 模型配置管理 (开发中)\ncurl -X GET http://localhost:8080/api/v1/admin/models/ \\\n  -H \"Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1c2VyX2lkIjoxLCJyb2xlIjoiYWRtaW4iLCJpc3MiOiJzb2x2ZS1nby1hcGkiLCJleHAiOjE3NTAyMzQ3MDIsIm5iZiI6MTc1MDE0ODMwMiwiaWF0IjoxNzUwMTQ4MzAyfQ.h9MDbNktI4QLbyoXJ7WVaBzTAGwlSiCP3NRDBDfMnoE\""

2025/06/18 12:43:18 [32m/Users/<USER>/Documents/Project/Solve/solve-go-api/internal/database/mysql.go:47
[0m[33m[29.436ms] [34;1m[rows:-][0m SELECT DATABASE()

2025/06/18 12:43:18 [32m/Users/<USER>/Documents/Project/Solve/solve-go-api/internal/database/mysql.go:47
[0m[33m[59.765ms] [34;1m[rows:1][0m SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'solve_web%' ORDER BY SCHEMA_NAME='solve_web' DESC,SCHEMA_NAME limit 1

2025/06/18 12:43:18 [32m/Users/<USER>/Documents/Project/Solve/solve-go-api/internal/database/mysql.go:47
[0m[33m[60.231ms] [34;1m[rows:-][0m SELECT count(*) FROM information_schema.tables WHERE table_schema = 'solve_web' AND table_name = 'hook_user' AND table_type = 'BASE TABLE'

2025/06/18 12:43:18 [32m/Users/<USER>/Documents/Project/Solve/solve-go-api/internal/database/mysql.go:47
[0m[33m[29.380ms] [34;1m[rows:-][0m SELECT DATABASE()

2025/06/18 12:43:18 [32m/Users/<USER>/Documents/Project/Solve/solve-go-api/internal/database/mysql.go:47
[0m[33m[60.076ms] [34;1m[rows:1][0m SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'solve_web%' ORDER BY SCHEMA_NAME='solve_web' DESC,SCHEMA_NAME limit 1

2025/06/18 12:43:18 [32m/Users/<USER>/Documents/Project/Solve/solve-go-api/internal/database/mysql.go:47
[0m[33m[59.808ms] [34;1m[rows:-][0m SELECT * FROM `hook_user` LIMIT 1

2025/06/18 12:43:18 [32m/Users/<USER>/Documents/Project/Solve/solve-go-api/internal/database/mysql.go:47
[0m[33m[58.977ms] [34;1m[rows:-][0m SELECT column_name, column_default, is_nullable = 'YES', data_type, character_maximum_length, column_type, column_key, extra, column_comment, numeric_precision, numeric_scale , datetime_precision FROM information_schema.columns WHERE table_schema = 'solve_web' AND table_name = 'hook_user' ORDER BY ORDINAL_POSITION

2025/06/18 12:43:18 [32m
[0m[33m[29.944ms] [34;1m[rows:-][0m SELECT DATABASE()

2025/06/18 12:43:18 [32m
[0m[33m[60.153ms] [34;1m[rows:1][0m SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'solve_web%' ORDER BY SCHEMA_NAME='solve_web' DESC,SCHEMA_NAME limit 1

2025/06/18 12:43:18 [32m/Users/<USER>/Documents/Project/Solve/solve-go-api/internal/database/mysql.go:47
[0m[33m[59.210ms] [34;1m[rows:2][0m 
SELECT
	TABLE_NAME,
	COLUMN_NAME,
	INDEX_NAME,
	NON_UNIQUE 
FROM
	information_schema.STATISTICS 
WHERE
	TABLE_SCHEMA = 'solve_web' 
	AND TABLE_NAME = 'hook_user' 
ORDER BY
	INDEX_NAME,
	SEQ_IN_INDEX

2025/06/18 12:43:18 [32m
[0m[33m[30.195ms] [34;1m[rows:-][0m SELECT DATABASE()

2025/06/18 12:43:18 [32m
[0m[33m[59.715ms] [34;1m[rows:1][0m SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'solve_web%' ORDER BY SCHEMA_NAME='solve_web' DESC,SCHEMA_NAME limit 1

2025/06/18 12:43:18 [32m
[0m[33m[60.009ms] [34;1m[rows:-][0m SELECT count(*) FROM INFORMATION_SCHEMA.table_constraints WHERE constraint_schema = 'solve_web' AND table_name = 'hook_user' AND constraint_name = 'uni_hook_user_phone'

2025/06/18 12:43:18 [32m
[0m[33m[30.002ms] [34;1m[rows:-][0m SELECT DATABASE()

2025/06/18 12:43:18 [32m
[0m[33m[59.945ms] [34;1m[rows:1][0m SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'solve_web%' ORDER BY SCHEMA_NAME='solve_web' DESC,SCHEMA_NAME limit 1

2025/06/18 12:43:18 [32m
[0m[33m[59.757ms] [34;1m[rows:-][0m SELECT count(*) FROM information_schema.statistics WHERE table_schema = 'solve_web' AND table_name = 'hook_user' AND index_name = 'idx_hook_user_phone'

2025/06/18 12:43:18 [32m/Users/<USER>/Documents/Project/Solve/solve-go-api/internal/database/mysql.go:47
[0m[33m[30.211ms] [34;1m[rows:-][0m SELECT DATABASE()

2025/06/18 12:43:18 [32m/Users/<USER>/Documents/Project/Solve/solve-go-api/internal/database/mysql.go:47
[0m[33m[59.280ms] [34;1m[rows:1][0m SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'solve_web%' ORDER BY SCHEMA_NAME='solve_web' DESC,SCHEMA_NAME limit 1

2025/06/18 12:43:18 [32m/Users/<USER>/Documents/Project/Solve/solve-go-api/internal/database/mysql.go:47
[0m[33m[59.597ms] [34;1m[rows:-][0m SELECT count(*) FROM information_schema.statistics WHERE table_schema = 'solve_web' AND table_name = 'hook_user' AND index_name = 'idx_hook_user_phone'

2025/06/18 12:43:19 [32m/Users/<USER>/Documents/Project/Solve/solve-go-api/internal/database/mysql.go:47
[0m[33m[29.846ms] [34;1m[rows:-][0m SELECT DATABASE()

2025/06/18 12:43:19 [32m/Users/<USER>/Documents/Project/Solve/solve-go-api/internal/database/mysql.go:47
[0m[33m[59.785ms] [34;1m[rows:1][0m SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'solve_web%' ORDER BY SCHEMA_NAME='solve_web' DESC,SCHEMA_NAME limit 1

2025/06/18 12:43:19 [32m/Users/<USER>/Documents/Project/Solve/solve-go-api/internal/database/mysql.go:47
[0m[33m[59.752ms] [34;1m[rows:-][0m SELECT count(*) FROM information_schema.tables WHERE table_schema = 'solve_web' AND table_name = 'hook_apps' AND table_type = 'BASE TABLE'

2025/06/18 12:43:19 [32m/Users/<USER>/Documents/Project/Solve/solve-go-api/internal/database/mysql.go:47
[0m[33m[29.862ms] [34;1m[rows:-][0m SELECT DATABASE()

2025/06/18 12:43:19 [32m/Users/<USER>/Documents/Project/Solve/solve-go-api/internal/database/mysql.go:47
[0m[33m[59.892ms] [34;1m[rows:1][0m SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'solve_web%' ORDER BY SCHEMA_NAME='solve_web' DESC,SCHEMA_NAME limit 1

2025/06/18 12:43:19 [32m/Users/<USER>/Documents/Project/Solve/solve-go-api/internal/database/mysql.go:47
[0m[33m[59.739ms] [34;1m[rows:-][0m SELECT * FROM `hook_apps` LIMIT 1

2025/06/18 12:43:19 [32m/Users/<USER>/Documents/Project/Solve/solve-go-api/internal/database/mysql.go:47
[0m[33m[59.571ms] [34;1m[rows:-][0m SELECT column_name, column_default, is_nullable = 'YES', data_type, character_maximum_length, column_type, column_key, extra, column_comment, numeric_precision, numeric_scale , datetime_precision FROM information_schema.columns WHERE table_schema = 'solve_web' AND table_name = 'hook_apps' ORDER BY ORDINAL_POSITION

2025/06/18 12:43:19 [32m
[0m[33m[29.278ms] [34;1m[rows:-][0m SELECT DATABASE()

2025/06/18 12:43:19 [32m
[0m[33m[59.902ms] [34;1m[rows:1][0m SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'solve_web%' ORDER BY SCHEMA_NAME='solve_web' DESC,SCHEMA_NAME limit 1

2025/06/18 12:43:19 [32m/Users/<USER>/Documents/Project/Solve/solve-go-api/internal/database/mysql.go:47
[0m[33m[59.796ms] [34;1m[rows:3][0m 
SELECT
	TABLE_NAME,
	COLUMN_NAME,
	INDEX_NAME,
	NON_UNIQUE 
FROM
	information_schema.STATISTICS 
WHERE
	TABLE_SCHEMA = 'solve_web' 
	AND TABLE_NAME = 'hook_apps' 
ORDER BY
	INDEX_NAME,
	SEQ_IN_INDEX

2025/06/18 12:43:19 [32m
[0m[33m[29.682ms] [34;1m[rows:-][0m SELECT DATABASE()

2025/06/18 12:43:19 [32m
[0m[33m[59.943ms] [34;1m[rows:1][0m SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'solve_web%' ORDER BY SCHEMA_NAME='solve_web' DESC,SCHEMA_NAME limit 1

2025/06/18 12:43:19 [32m
[0m[33m[59.606ms] [34;1m[rows:-][0m SELECT count(*) FROM INFORMATION_SCHEMA.table_constraints WHERE constraint_schema = 'solve_web' AND table_name = 'hook_apps' AND constraint_name = 'uni_hook_apps_app_id'

2025/06/18 12:43:19 [32m
[0m[33m[31.365ms] [34;1m[rows:-][0m SELECT DATABASE()

2025/06/18 12:43:19 [32m
[0m[33m[58.302ms] [34;1m[rows:1][0m SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'solve_web%' ORDER BY SCHEMA_NAME='solve_web' DESC,SCHEMA_NAME limit 1

2025/06/18 12:43:19 [32m
[0m[33m[59.791ms] [34;1m[rows:-][0m SELECT count(*) FROM information_schema.statistics WHERE table_schema = 'solve_web' AND table_name = 'hook_apps' AND index_name = 'idx_hook_apps_app_id'

2025/06/18 12:43:19 [32m/Users/<USER>/Documents/Project/Solve/solve-go-api/internal/database/mysql.go:47
[0m[33m[29.622ms] [34;1m[rows:-][0m SELECT DATABASE()

2025/06/18 12:43:19 [32m/Users/<USER>/Documents/Project/Solve/solve-go-api/internal/database/mysql.go:47
[0m[33m[59.947ms] [34;1m[rows:1][0m SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'solve_web%' ORDER BY SCHEMA_NAME='solve_web' DESC,SCHEMA_NAME limit 1

2025/06/18 12:43:19 [32m/Users/<USER>/Documents/Project/Solve/solve-go-api/internal/database/mysql.go:47
[0m[33m[59.467ms] [34;1m[rows:-][0m SELECT count(*) FROM information_schema.statistics WHERE table_schema = 'solve_web' AND table_name = 'hook_apps' AND index_name = 'idx_hook_apps_user_id'

2025/06/18 12:43:19 [32m/Users/<USER>/Documents/Project/Solve/solve-go-api/internal/database/mysql.go:47
[0m[33m[29.774ms] [34;1m[rows:-][0m SELECT DATABASE()

2025/06/18 12:43:20 [32m/Users/<USER>/Documents/Project/Solve/solve-go-api/internal/database/mysql.go:47
[0m[33m[60.026ms] [34;1m[rows:1][0m SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'solve_web%' ORDER BY SCHEMA_NAME='solve_web' DESC,SCHEMA_NAME limit 1

2025/06/18 12:43:20 [32m/Users/<USER>/Documents/Project/Solve/solve-go-api/internal/database/mysql.go:47
[0m[33m[59.593ms] [34;1m[rows:-][0m SELECT count(*) FROM information_schema.statistics WHERE table_schema = 'solve_web' AND table_name = 'hook_apps' AND index_name = 'idx_hook_apps_app_id'

2025/06/18 12:43:20 [32m/Users/<USER>/Documents/Project/Solve/solve-go-api/internal/database/mysql.go:47
[0m[33m[29.756ms] [34;1m[rows:-][0m SELECT DATABASE()

2025/06/18 12:43:20 [32m/Users/<USER>/Documents/Project/Solve/solve-go-api/internal/database/mysql.go:47
[0m[33m[60.058ms] [34;1m[rows:1][0m SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'solve_web%' ORDER BY SCHEMA_NAME='solve_web' DESC,SCHEMA_NAME limit 1

2025/06/18 12:43:20 [32m/Users/<USER>/Documents/Project/Solve/solve-go-api/internal/database/mysql.go:47
[0m[33m[59.521ms] [34;1m[rows:-][0m SELECT count(*) FROM information_schema.tables WHERE table_schema = 'solve_web' AND table_name = 'hook_balance_logs' AND table_type = 'BASE TABLE'

2025/06/18 12:43:20 [32m/Users/<USER>/Documents/Project/Solve/solve-go-api/internal/database/mysql.go:47
[0m[33m[29.876ms] [34;1m[rows:-][0m SELECT DATABASE()

2025/06/18 12:43:20 [32m/Users/<USER>/Documents/Project/Solve/solve-go-api/internal/database/mysql.go:47
[0m[33m[59.870ms] [34;1m[rows:1][0m SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'solve_web%' ORDER BY SCHEMA_NAME='solve_web' DESC,SCHEMA_NAME limit 1

2025/06/18 12:43:20 [32m/Users/<USER>/Documents/Project/Solve/solve-go-api/internal/database/mysql.go:47
[0m[33m[59.824ms] [34;1m[rows:-][0m SELECT * FROM `hook_balance_logs` LIMIT 1

2025/06/18 12:43:20 [32m/Users/<USER>/Documents/Project/Solve/solve-go-api/internal/database/mysql.go:47
[0m[33m[59.915ms] [34;1m[rows:-][0m SELECT column_name, column_default, is_nullable = 'YES', data_type, character_maximum_length, column_type, column_key, extra, column_comment, numeric_precision, numeric_scale , datetime_precision FROM information_schema.columns WHERE table_schema = 'solve_web' AND table_name = 'hook_balance_logs' ORDER BY ORDINAL_POSITION

2025/06/18 12:43:20 [32m/Users/<USER>/Documents/Project/Solve/solve-go-api/internal/database/mysql.go:47
[0m[33m[29.627ms] [34;1m[rows:-][0m SELECT DATABASE()

2025/06/18 12:43:20 [32m/Users/<USER>/Documents/Project/Solve/solve-go-api/internal/database/mysql.go:47
[0m[33m[59.919ms] [34;1m[rows:1][0m SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'solve_web%' ORDER BY SCHEMA_NAME='solve_web' DESC,SCHEMA_NAME limit 1

2025/06/18 12:43:20 [32m/Users/<USER>/Documents/Project/Solve/solve-go-api/internal/database/mysql.go:47
[0m[33m[59.608ms] [34;1m[rows:-][0m SELECT count(*) FROM information_schema.statistics WHERE table_schema = 'solve_web' AND table_name = 'hook_balance_logs' AND index_name = 'idx_hook_balance_logs_user_id'

2025/06/18 12:43:20 [32m/Users/<USER>/Documents/Project/Solve/solve-go-api/internal/database/mysql.go:47
[0m[33m[29.763ms] [34;1m[rows:-][0m SELECT DATABASE()

2025/06/18 12:43:20 [32m/Users/<USER>/Documents/Project/Solve/solve-go-api/internal/database/mysql.go:47
[0m[33m[60.102ms] [34;1m[rows:1][0m SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'solve_web%' ORDER BY SCHEMA_NAME='solve_web' DESC,SCHEMA_NAME limit 1

2025/06/18 12:43:20 [32m/Users/<USER>/Documents/Project/Solve/solve-go-api/internal/database/mysql.go:47
[0m[33m[59.805ms] [34;1m[rows:-][0m SELECT count(*) FROM information_schema.tables WHERE table_schema = 'solve_web' AND table_name = 'hook_system_config' AND table_type = 'BASE TABLE'

2025/06/18 12:43:20 [32m/Users/<USER>/Documents/Project/Solve/solve-go-api/internal/database/mysql.go:47
[0m[33m[29.506ms] [34;1m[rows:-][0m SELECT DATABASE()

2025/06/18 12:43:20 [32m/Users/<USER>/Documents/Project/Solve/solve-go-api/internal/database/mysql.go:47
[0m[33m[60.102ms] [34;1m[rows:1][0m SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'solve_web%' ORDER BY SCHEMA_NAME='solve_web' DESC,SCHEMA_NAME limit 1

2025/06/18 12:43:20 [32m/Users/<USER>/Documents/Project/Solve/solve-go-api/internal/database/mysql.go:47
[0m[33m[59.932ms] [34;1m[rows:-][0m SELECT * FROM `hook_system_config` LIMIT 1

2025/06/18 12:43:20 [32m/Users/<USER>/Documents/Project/Solve/solve-go-api/internal/database/mysql.go:47
[0m[33m[59.857ms] [34;1m[rows:-][0m SELECT column_name, column_default, is_nullable = 'YES', data_type, character_maximum_length, column_type, column_key, extra, column_comment, numeric_precision, numeric_scale , datetime_precision FROM information_schema.columns WHERE table_schema = 'solve_web' AND table_name = 'hook_system_config' ORDER BY ORDINAL_POSITION

2025/06/18 12:43:20 [32m
[0m[33m[29.711ms] [34;1m[rows:-][0m SELECT DATABASE()

2025/06/18 12:43:21 [32m
[0m[33m[59.288ms] [34;1m[rows:1][0m SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'solve_web%' ORDER BY SCHEMA_NAME='solve_web' DESC,SCHEMA_NAME limit 1

2025/06/18 12:43:21 [32m/Users/<USER>/Documents/Project/Solve/solve-go-api/internal/database/mysql.go:47
[0m[33m[62.487ms] [34;1m[rows:2][0m 
SELECT
	TABLE_NAME,
	COLUMN_NAME,
	INDEX_NAME,
	NON_UNIQUE 
FROM
	information_schema.STATISTICS 
WHERE
	TABLE_SCHEMA = 'solve_web' 
	AND TABLE_NAME = 'hook_system_config' 
ORDER BY
	INDEX_NAME,
	SEQ_IN_INDEX

2025/06/18 12:43:21 [32m
[0m[33m[27.438ms] [34;1m[rows:-][0m SELECT DATABASE()

2025/06/18 12:43:21 [32m
[0m[33m[60.129ms] [34;1m[rows:1][0m SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'solve_web%' ORDER BY SCHEMA_NAME='solve_web' DESC,SCHEMA_NAME limit 1

2025/06/18 12:43:21 [32m
[0m[33m[59.759ms] [34;1m[rows:-][0m SELECT count(*) FROM INFORMATION_SCHEMA.table_constraints WHERE constraint_schema = 'solve_web' AND table_name = 'hook_system_config' AND constraint_name = 'uni_hook_system_config_key'

2025/06/18 12:43:21 [32m
[0m[33m[29.516ms] [34;1m[rows:-][0m SELECT DATABASE()

2025/06/18 12:43:21 [32m
[0m[33m[59.986ms] [34;1m[rows:1][0m SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'solve_web%' ORDER BY SCHEMA_NAME='solve_web' DESC,SCHEMA_NAME limit 1

2025/06/18 12:43:21 [32m
[0m[33m[59.953ms] [34;1m[rows:-][0m SELECT count(*) FROM information_schema.statistics WHERE table_schema = 'solve_web' AND table_name = 'hook_system_config' AND index_name = 'idx_hook_system_config_key'

2025/06/18 12:43:21 [32m/Users/<USER>/Documents/Project/Solve/solve-go-api/internal/database/mysql.go:47
[0m[33m[29.452ms] [34;1m[rows:-][0m SELECT DATABASE()

2025/06/18 12:43:21 [32m/Users/<USER>/Documents/Project/Solve/solve-go-api/internal/database/mysql.go:47
[0m[33m[59.888ms] [34;1m[rows:1][0m SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'solve_web%' ORDER BY SCHEMA_NAME='solve_web' DESC,SCHEMA_NAME limit 1

2025/06/18 12:43:21 [32m/Users/<USER>/Documents/Project/Solve/solve-go-api/internal/database/mysql.go:47
[0m[33m[60.038ms] [34;1m[rows:-][0m SELECT count(*) FROM information_schema.statistics WHERE table_schema = 'solve_web' AND table_name = 'hook_system_config' AND index_name = 'idx_hook_system_config_key'

2025/06/18 12:43:21 [32m/Users/<USER>/Documents/Project/Solve/solve-go-api/internal/database/mysql.go:47
[0m[33m[29.441ms] [34;1m[rows:-][0m SELECT DATABASE()

2025/06/18 12:43:21 [32m/Users/<USER>/Documents/Project/Solve/solve-go-api/internal/database/mysql.go:47
[0m[33m[59.924ms] [34;1m[rows:1][0m SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'solve_web%' ORDER BY SCHEMA_NAME='solve_web' DESC,SCHEMA_NAME limit 1

2025/06/18 12:43:21 [32m/Users/<USER>/Documents/Project/Solve/solve-go-api/internal/database/mysql.go:47
[0m[33m[59.935ms] [34;1m[rows:-][0m SELECT count(*) FROM information_schema.tables WHERE table_schema = 'solve_web' AND table_name = 'hook_models' AND table_type = 'BASE TABLE'

2025/06/18 12:43:21 [32m/Users/<USER>/Documents/Project/Solve/solve-go-api/internal/database/mysql.go:47
[0m[33m[29.640ms] [34;1m[rows:-][0m SELECT DATABASE()

2025/06/18 12:43:21 [32m/Users/<USER>/Documents/Project/Solve/solve-go-api/internal/database/mysql.go:47
[0m[33m[59.949ms] [34;1m[rows:1][0m SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'solve_web%' ORDER BY SCHEMA_NAME='solve_web' DESC,SCHEMA_NAME limit 1

2025/06/18 12:43:21 [32m/Users/<USER>/Documents/Project/Solve/solve-go-api/internal/database/mysql.go:47
[0m[33m[60.081ms] [34;1m[rows:-][0m SELECT * FROM `hook_models` LIMIT 1

2025/06/18 12:43:21 [32m/Users/<USER>/Documents/Project/Solve/solve-go-api/internal/database/mysql.go:47
[0m[33m[59.565ms] [34;1m[rows:-][0m SELECT column_name, column_default, is_nullable = 'YES', data_type, character_maximum_length, column_type, column_key, extra, column_comment, numeric_precision, numeric_scale , datetime_precision FROM information_schema.columns WHERE table_schema = 'solve_web' AND table_name = 'hook_models' ORDER BY ORDINAL_POSITION

2025/06/18 12:43:21 [32m
[0m[33m[29.485ms] [34;1m[rows:-][0m SELECT DATABASE()

2025/06/18 12:43:22 [32m
[0m[33m[60.061ms] [34;1m[rows:1][0m SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'solve_web%' ORDER BY SCHEMA_NAME='solve_web' DESC,SCHEMA_NAME limit 1

2025/06/18 12:43:22 [32m/Users/<USER>/Documents/Project/Solve/solve-go-api/internal/database/mysql.go:47
[0m[33m[59.782ms] [34;1m[rows:2][0m 
SELECT
	TABLE_NAME,
	COLUMN_NAME,
	INDEX_NAME,
	NON_UNIQUE 
FROM
	information_schema.STATISTICS 
WHERE
	TABLE_SCHEMA = 'solve_web' 
	AND TABLE_NAME = 'hook_models' 
ORDER BY
	INDEX_NAME,
	SEQ_IN_INDEX

2025/06/18 12:43:22 [32m
[0m[33m[29.763ms] [34;1m[rows:-][0m SELECT DATABASE()

2025/06/18 12:43:22 [32m
[0m[33m[60.051ms] [34;1m[rows:1][0m SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'solve_web%' ORDER BY SCHEMA_NAME='solve_web' DESC,SCHEMA_NAME limit 1

2025/06/18 12:43:22 [32m
[0m[33m[59.664ms] [34;1m[rows:-][0m SELECT count(*) FROM INFORMATION_SCHEMA.table_constraints WHERE constraint_schema = 'solve_web' AND table_name = 'hook_models' AND constraint_name = 'uni_hook_models_model_name'

2025/06/18 12:43:22 [32m
[0m[33m[29.701ms] [34;1m[rows:-][0m SELECT DATABASE()

2025/06/18 12:43:22 [32m
[0m[33m[60.161ms] [34;1m[rows:1][0m SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'solve_web%' ORDER BY SCHEMA_NAME='solve_web' DESC,SCHEMA_NAME limit 1

2025/06/18 12:43:22 [32m
[0m[33m[59.566ms] [34;1m[rows:-][0m SELECT count(*) FROM information_schema.statistics WHERE table_schema = 'solve_web' AND table_name = 'hook_models' AND index_name = 'idx_hook_models_model_name'

2025/06/18 12:43:22 [32m/Users/<USER>/Documents/Project/Solve/solve-go-api/internal/database/mysql.go:47
[0m[33m[39.757ms] [34;1m[rows:0][0m ALTER TABLE `hook_models` MODIFY COLUMN `temperature` decimal(3,2) DEFAULT 0.7 COMMENT '温度参数'

2025/06/18 12:43:22 [32m/Users/<USER>/Documents/Project/Solve/solve-go-api/internal/database/mysql.go:47
[0m[33m[39.282ms] [34;1m[rows:0][0m ALTER TABLE `hook_models` MODIFY COLUMN `top_p` decimal(3,2) DEFAULT 0.9 COMMENT 'TopP参数'

2025/06/18 12:43:22 [32m/Users/<USER>/Documents/Project/Solve/solve-go-api/internal/database/mysql.go:47
[0m[33m[39.944ms] [34;1m[rows:0][0m ALTER TABLE `hook_models` MODIFY COLUMN `repetition_penalty` decimal(3,2) DEFAULT 1 COMMENT '重复惩罚'

2025/06/18 12:43:22 [32m/Users/<USER>/Documents/Project/Solve/solve-go-api/internal/database/mysql.go:47
[0m[33m[39.766ms] [34;1m[rows:0][0m ALTER TABLE `hook_models` MODIFY COLUMN `presence_penalty` decimal(3,2) DEFAULT 0 COMMENT '存在惩罚'

2025/06/18 12:43:22 [32m/Users/<USER>/Documents/Project/Solve/solve-go-api/internal/database/mysql.go:47
[0m[33m[30.223ms] [34;1m[rows:-][0m SELECT DATABASE()

2025/06/18 12:43:22 [32m/Users/<USER>/Documents/Project/Solve/solve-go-api/internal/database/mysql.go:47
[0m[33m[59.849ms] [34;1m[rows:1][0m SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'solve_web%' ORDER BY SCHEMA_NAME='solve_web' DESC,SCHEMA_NAME limit 1

2025/06/18 12:43:22 [32m/Users/<USER>/Documents/Project/Solve/solve-go-api/internal/database/mysql.go:47
[0m[33m[59.932ms] [34;1m[rows:-][0m SELECT count(*) FROM information_schema.statistics WHERE table_schema = 'solve_web' AND table_name = 'hook_models' AND index_name = 'idx_hook_models_model_name'

2025/06/18 12:43:22 [32m/Users/<USER>/Documents/Project/Solve/solve-go-api/internal/database/mysql.go:47
[0m[33m[29.228ms] [34;1m[rows:-][0m SELECT DATABASE()

2025/06/18 12:43:22 [32m/Users/<USER>/Documents/Project/Solve/solve-go-api/internal/database/mysql.go:47
[0m[33m[61.462ms] [34;1m[rows:1][0m SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'solve_web%' ORDER BY SCHEMA_NAME='solve_web' DESC,SCHEMA_NAME limit 1

2025/06/18 12:43:22 [32m/Users/<USER>/Documents/Project/Solve/solve-go-api/internal/database/mysql.go:47
[0m[33m[58.062ms] [34;1m[rows:-][0m SELECT count(*) FROM information_schema.tables WHERE table_schema = 'solve_web' AND table_name = 'hook_question_bank' AND table_type = 'BASE TABLE'

2025/06/18 12:43:22 [32m/Users/<USER>/Documents/Project/Solve/solve-go-api/internal/database/mysql.go:47
[0m[33m[29.868ms] [34;1m[rows:-][0m SELECT DATABASE()

2025/06/18 12:43:22 [32m/Users/<USER>/Documents/Project/Solve/solve-go-api/internal/database/mysql.go:47
[0m[33m[59.818ms] [34;1m[rows:1][0m SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'solve_web%' ORDER BY SCHEMA_NAME='solve_web' DESC,SCHEMA_NAME limit 1

2025/06/18 12:43:22 [32m/Users/<USER>/Documents/Project/Solve/solve-go-api/internal/database/mysql.go:47
[0m[33m[59.892ms] [34;1m[rows:-][0m SELECT * FROM `hook_question_bank` LIMIT 1

2025/06/18 12:43:23 [32m/Users/<USER>/Documents/Project/Solve/solve-go-api/internal/database/mysql.go:47
[0m[33m[59.695ms] [34;1m[rows:-][0m SELECT column_name, column_default, is_nullable = 'YES', data_type, character_maximum_length, column_type, column_key, extra, column_comment, numeric_precision, numeric_scale , datetime_precision FROM information_schema.columns WHERE table_schema = 'solve_web' AND table_name = 'hook_question_bank' ORDER BY ORDINAL_POSITION

2025/06/18 12:43:23 [32m/Users/<USER>/Documents/Project/Solve/solve-go-api/internal/database/mysql.go:47
[0m[33m[29.248ms] [34;1m[rows:-][0m SELECT DATABASE()

2025/06/18 12:43:23 [32m/Users/<USER>/Documents/Project/Solve/solve-go-api/internal/database/mysql.go:47
[0m[33m[59.821ms] [34;1m[rows:1][0m SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'solve_web%' ORDER BY SCHEMA_NAME='solve_web' DESC,SCHEMA_NAME limit 1

2025/06/18 12:43:23 [32m/Users/<USER>/Documents/Project/Solve/solve-go-api/internal/database/mysql.go:47
[0m[33m[59.810ms] [34;1m[rows:-][0m SELECT count(*) FROM information_schema.statistics WHERE table_schema = 'solve_web' AND table_name = 'hook_question_bank' AND index_name = 'idx_hook_question_bank_hash_key'

2025/06/18 12:43:23 [32m/Users/<USER>/Documents/Project/Solve/solve-go-api/internal/database/mysql.go:47
[0m[33m[29.735ms] [34;1m[rows:-][0m SELECT DATABASE()

2025/06/18 12:43:23 [32m/Users/<USER>/Documents/Project/Solve/solve-go-api/internal/database/mysql.go:47
[0m[33m[59.844ms] [34;1m[rows:1][0m SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'solve_web%' ORDER BY SCHEMA_NAME='solve_web' DESC,SCHEMA_NAME limit 1

2025/06/18 12:43:23 [32m/Users/<USER>/Documents/Project/Solve/solve-go-api/internal/database/mysql.go:47
[0m[33m[59.818ms] [34;1m[rows:-][0m SELECT count(*) FROM information_schema.tables WHERE table_schema = 'solve_web' AND table_name = 'hook_solve_logs' AND table_type = 'BASE TABLE'

2025/06/18 12:43:23 [32m/Users/<USER>/Documents/Project/Solve/solve-go-api/internal/database/mysql.go:47
[0m[33m[29.832ms] [34;1m[rows:-][0m SELECT DATABASE()

2025/06/18 12:43:23 [32m/Users/<USER>/Documents/Project/Solve/solve-go-api/internal/database/mysql.go:47
[0m[33m[60.025ms] [34;1m[rows:1][0m SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'solve_web%' ORDER BY SCHEMA_NAME='solve_web' DESC,SCHEMA_NAME limit 1

2025/06/18 12:43:23 [32m/Users/<USER>/Documents/Project/Solve/solve-go-api/internal/database/mysql.go:47
[0m[33m[59.704ms] [34;1m[rows:-][0m SELECT * FROM `hook_solve_logs` LIMIT 1

2025/06/18 12:43:23 [32m/Users/<USER>/Documents/Project/Solve/solve-go-api/internal/database/mysql.go:47
[0m[33m[60.488ms] [34;1m[rows:-][0m SELECT column_name, column_default, is_nullable = 'YES', data_type, character_maximum_length, column_type, column_key, extra, column_comment, numeric_precision, numeric_scale , datetime_precision FROM information_schema.columns WHERE table_schema = 'solve_web' AND table_name = 'hook_solve_logs' ORDER BY ORDINAL_POSITION

2025/06/18 12:43:23 [32m/Users/<USER>/Documents/Project/Solve/solve-go-api/internal/database/mysql.go:47
[0m[33m[28.779ms] [34;1m[rows:-][0m SELECT DATABASE()

2025/06/18 12:43:23 [32m/Users/<USER>/Documents/Project/Solve/solve-go-api/internal/database/mysql.go:47
[0m[33m[59.886ms] [34;1m[rows:1][0m SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'solve_web%' ORDER BY SCHEMA_NAME='solve_web' DESC,SCHEMA_NAME limit 1

2025/06/18 12:43:23 [32m/Users/<USER>/Documents/Project/Solve/solve-go-api/internal/database/mysql.go:47
[0m[33m[59.671ms] [34;1m[rows:-][0m SELECT count(*) FROM information_schema.statistics WHERE table_schema = 'solve_web' AND table_name = 'hook_solve_logs' AND index_name = 'idx_hook_solve_logs_app_id'

2025/06/18 12:43:23 [32m/Users/<USER>/Documents/Project/Solve/solve-go-api/internal/database/mysql.go:67
[0m[33m[60.152ms] [34;1m[rows:1][0m SELECT count(*) FROM `hook_user` WHERE role IN ('admin','manager')
[GIN-debug] [WARNING] Creating an Engine instance with the Logger and Recovery middleware already attached.

[GIN-debug] [WARNING] Running in "debug" mode. Switch to "release" mode in production.
 - using env:	export GIN_MODE=release
 - using code:	gin.SetMode(gin.ReleaseMode)

[GIN-debug] GET    /health                   --> solve-go-api/internal/router.Setup.func1 (6 handlers)
[GIN-debug] POST   /api/v1/send-sms          --> solve-go-api/internal/handlers.(*AuthHandler).SendSMSCode-fm (6 handlers)
[GIN-debug] POST   /api/v1/register          --> solve-go-api/internal/handlers.(*AuthHandler).Register-fm (6 handlers)
[GIN-debug] POST   /api/v1/login             --> solve-go-api/internal/handlers.(*AuthHandler).Login-fm (6 handlers)
[GIN-debug] POST   /api/v1/forgot-password   --> solve-go-api/internal/handlers.(*AuthHandler).ForgotPassword-fm (6 handlers)
[GIN-debug] POST   /api/v1/reset-password    --> solve-go-api/internal/handlers.(*AuthHandler).ResetPassword-fm (6 handlers)
[GIN-debug] POST   /api/v1/solve/question    --> solve-go-api/internal/handlers.(*SolveHandler).SolveQuestion-fm (7 handlers)
[GIN-debug] GET    /api/v1/user/profile      --> solve-go-api/internal/handlers.(*AuthHandler).GetProfile-fm (7 handlers)
[GIN-debug] PUT    /api/v1/user/profile      --> solve-go-api/internal/handlers.(*AuthHandler).UpdateProfile-fm (7 handlers)
[GIN-debug] POST   /api/v1/user/change-password --> solve-go-api/internal/handlers.(*AuthHandler).ChangePassword-fm (7 handlers)
[GIN-debug] GET    /api/v1/user/balance-logs --> solve-go-api/internal/handlers.(*AuthHandler).GetBalanceLogs-fm (7 handlers)
[GIN-debug] GET    /api/v1/apps/             --> solve-go-api/internal/handlers.(*AppHandler).GetApps-fm (7 handlers)
[GIN-debug] POST   /api/v1/apps/             --> solve-go-api/internal/handlers.(*AppHandler).CreateApp-fm (7 handlers)
[GIN-debug] PUT    /api/v1/apps/:id          --> solve-go-api/internal/handlers.(*AppHandler).UpdateApp-fm (7 handlers)
[GIN-debug] DELETE /api/v1/apps/:id          --> solve-go-api/internal/handlers.(*AppHandler).DeleteApp-fm (7 handlers)
[GIN-debug] POST   /api/v1/apps/:id/reset-secret --> solve-go-api/internal/handlers.(*AppHandler).ResetSecret-fm (7 handlers)
[GIN-debug] GET    /api/v1/apps/:id/logs     --> solve-go-api/internal/handlers.(*AppHandler).GetAppLogs-fm (7 handlers)
[GIN-debug] GET    /api/v1/manager/questions/ --> solve-go-api/internal/handlers.(*QuestionHandler).GetQuestions-fm (8 handlers)
[GIN-debug] GET    /api/v1/manager/questions/:id --> solve-go-api/internal/handlers.(*QuestionHandler).GetQuestion-fm (8 handlers)
[GIN-debug] PUT    /api/v1/manager/questions/:id --> solve-go-api/internal/handlers.(*QuestionHandler).UpdateQuestion-fm (8 handlers)
[GIN-debug] DELETE /api/v1/manager/questions/:id --> solve-go-api/internal/handlers.(*QuestionHandler).DeleteQuestion-fm (8 handlers)
[GIN-debug] POST   /api/v1/manager/questions/:id/verify --> solve-go-api/internal/handlers.(*QuestionHandler).VerifyQuestion-fm (8 handlers)
[GIN-debug] POST   /api/v1/manager/questions/:id/unverify --> solve-go-api/internal/handlers.(*QuestionHandler).UnverifyQuestion-fm (8 handlers)
[GIN-debug] GET    /api/v1/manager/questions/stats --> solve-go-api/internal/handlers.(*QuestionHandler).GetStats-fm (8 handlers)
[GIN-debug] GET    /api/v1/manager/logs/     --> solve-go-api/internal/handlers.(*LogHandler).GetLogs-fm (8 handlers)
[GIN-debug] GET    /api/v1/manager/logs/:id  --> solve-go-api/internal/handlers.(*LogHandler).GetLog-fm (8 handlers)
[GIN-debug] DELETE /api/v1/manager/logs/:id  --> solve-go-api/internal/handlers.(*LogHandler).DeleteLog-fm (8 handlers)
[GIN-debug] GET    /api/v1/manager/logs/stats --> solve-go-api/internal/handlers.(*LogHandler).GetStats-fm (8 handlers)
[GIN-debug] GET    /api/v1/admin/users/      --> solve-go-api/internal/handlers.(*AdminHandler).GetUsers-fm (8 handlers)
[GIN-debug] GET    /api/v1/admin/users/:id   --> solve-go-api/internal/handlers.(*AdminHandler).GetUser-fm (8 handlers)
[GIN-debug] PUT    /api/v1/admin/users/:id   --> solve-go-api/internal/handlers.(*AdminHandler).UpdateUser-fm (8 handlers)
[GIN-debug] POST   /api/v1/admin/users/:id/activate --> solve-go-api/internal/handlers.(*AdminHandler).ActivateUser-fm (8 handlers)
[GIN-debug] POST   /api/v1/admin/users/:id/deactivate --> solve-go-api/internal/handlers.(*AdminHandler).DeactivateUser-fm (8 handlers)
[GIN-debug] POST   /api/v1/admin/users/:id/recharge --> solve-go-api/internal/handlers.(*AdminHandler).RechargeUser-fm (8 handlers)
[GIN-debug] GET    /api/v1/admin/users/stats --> solve-go-api/internal/handlers.(*AdminHandler).GetUserStats-fm (8 handlers)
[GIN-debug] GET    /api/v1/admin/users/pending --> solve-go-api/internal/handlers.(*AdminHandler).GetPendingUsers-fm (8 handlers)
[GIN-debug] POST   /api/v1/admin/users/:id/approve --> solve-go-api/internal/handlers.(*AdminHandler).ApproveUser-fm (8 handlers)
[GIN-debug] POST   /api/v1/admin/users/:id/reject --> solve-go-api/internal/handlers.(*AdminHandler).RejectUser-fm (8 handlers)
[GIN-debug] GET    /api/v1/admin/apps/       --> solve-go-api/internal/handlers.(*AdminHandler).GetAllApps-fm (8 handlers)
[GIN-debug] PUT    /api/v1/admin/apps/:id    --> solve-go-api/internal/handlers.(*AdminHandler).UpdateApp-fm (8 handlers)
[GIN-debug] POST   /api/v1/admin/apps/:id/activate --> solve-go-api/internal/handlers.(*AdminHandler).ActivateApp-fm (8 handlers)
[GIN-debug] POST   /api/v1/admin/apps/:id/deactivate --> solve-go-api/internal/handlers.(*AdminHandler).DeactivateApp-fm (8 handlers)
[GIN-debug] GET    /api/v1/admin/config/     --> solve-go-api/internal/handlers.(*AdminHandler).GetConfigs-fm (8 handlers)
[GIN-debug] PUT    /api/v1/admin/config/:key --> solve-go-api/internal/handlers.(*AdminHandler).UpdateConfig-fm (8 handlers)
[GIN-debug] GET    /api/v1/admin/models/     --> solve-go-api/internal/handlers.(*AdminHandler).GetModels-fm (8 handlers)
[GIN-debug] POST   /api/v1/admin/models/     --> solve-go-api/internal/handlers.(*AdminHandler).CreateModel-fm (8 handlers)
[GIN-debug] PUT    /api/v1/admin/models/:id  --> solve-go-api/internal/handlers.(*AdminHandler).UpdateModel-fm (8 handlers)
[GIN-debug] DELETE /api/v1/admin/models/:id  --> solve-go-api/internal/handlers.(*AdminHandler).DeleteModel-fm (8 handlers)
2025/06/18 12:43:23.762449 main.go:92: Server starting on port 8080
[GIN-debug] [WARNING] You trusted all proxies, this is NOT safe. We recommend you to set a value.
Please check https://pkg.go.dev/github.com/gin-gonic/gin#readme-don-t-trust-all-proxies for details.
[GIN-debug] Listening and serving HTTP on :8080

2025/06/18 12:43:45 [32m/Users/<USER>/Documents/Project/Solve/solve-go-api/internal/services/app_service.go:95
[0m[33m[62.688ms] [34;1m[rows:1][0m SELECT * FROM `hook_apps` WHERE app_id = 'NdOCNqqWPtrLFvZj' AND app_secret = 'VsubbUlgZejHgRrylJ0qs3A9HJy3a8P2' ORDER BY `hook_apps`.`id` LIMIT 1

2025/06/18 12:43:45 [32m/Users/<USER>/Documents/Project/Solve/solve-go-api/internal/services/app_service.go:111
[0m[33m[59.775ms] [34;1m[rows:1][0m SELECT * FROM `hook_user` WHERE `hook_user`.`id` = 20 ORDER BY `hook_user`.`id` LIMIT 1
2025/06/18 12:43:45.391166 solve_handler.go:31: 🎯 [HANDLER] 收到解题请求 - IP: ::1, UserAgent: PostmanRuntime-ApipostRuntime/1.1.0
2025/06/18 12:43:45.391652 solve_handler.go:63: ✅ [HANDLER] 请求参数解析成功 - AppID: NdOCNqqWPtrLFvZj, ImageURL: http://img.igmdns.com/images/cc0001.jpg
2025/06/18 12:43:45.391695 solve_handler.go:66: 🚀 [HANDLER] 开始处理解题请求
2025/06/18 12:43:45.391755 solve_service.go:61: 🚀 [SOLVE] 开始处理解题请求 - AppID: NdOCNqqWPtrLFvZj, ImageURL: http://img.igmdns.com/images/cc0001.jpg
2025/06/18 12:43:45.391897 solve_service.go:64: 📋 [SOLVE] 步骤1: 验证应用和用户

2025/06/18 12:43:45 [32m/Users/<USER>/Documents/Project/Solve/solve-go-api/internal/services/app_service.go:53
[0m[33m[59.453ms] [34;1m[rows:1][0m SELECT * FROM `hook_apps` WHERE app_id = 'NdOCNqqWPtrLFvZj' ORDER BY `hook_apps`.`id` LIMIT 1

2025/06/18 12:43:45 [32m/Users/<USER>/Documents/Project/Solve/solve-go-api/internal/services/app_service.go:111
[0m[33m[59.943ms] [34;1m[rows:1][0m SELECT * FROM `hook_user` WHERE `hook_user`.`id` = 20 ORDER BY `hook_user`.`id` LIMIT 1
2025/06/18 12:43:45.511692 solve_service.go:70: ✅ [SOLVE] 应用/用户验证成功 - UserID: 20, Balance: 469100
2025/06/18 12:43:45.511783 solve_service.go:73: 💰 [SOLVE] 步骤2: 检查用户积分 - 当前积分: 469100
2025/06/18 12:43:45.511829 solve_service.go:81: 🔍 [SOLVE] 步骤3: 调用OCR模型识别图片
2025/06/18 12:43:45.511865 solve_service.go:170: 🔍 [OCR] 开始OCR处理 - 模型: qwen-vl-plus, 图片URL: http://img.igmdns.com/images/cc0001.jpg

2025/06/18 12:43:45 [32m/Users/<USER>/Documents/Project/Solve/solve-go-api/internal/services/model_service.go:39
[0m[33m[59.424ms] [34;1m[rows:1][0m SELECT * FROM `hook_models` WHERE model_name = 'qwen-vl-plus' ORDER BY `hook_models`.`id` LIMIT 1
2025/06/18 12:43:45.571644 model_service.go:217: 🔍 [HTTP-REQUEST] 模型: qwen-vl-plus
2025/06/18 12:43:45.571747 model_service.go:218: 🔍 [HTTP-REQUEST] 请求URL: https://dashscope.aliyuncs.com/api/v1/services/aigc/multimodal-generation/generation
2025/06/18 12:43:45.571779 model_service.go:219: 🔍 [HTTP-REQUEST] 请求体长度: 573 bytes
2025/06/18 12:43:45.571823 model_service.go:224: 🔍 [HTTP-REQUEST] 完整请求体:
{
  "input": {
    "messages": [
      {
        "content": "严格标准返回json格式。示例{\"qutext\":\"(题目类型)(序号)(题目正文)\",\"options\":{\"所有选项\"}}",
        "role": "system"
      },
      {
        "content": [
          {
            "image": "http://img.igmdns.com/images/cc0001.jpg"
          },
          {
            "text": "精准且完整的识别问题,严格按照要求输出的完整json,其中的options字段的格式必须正确,选项字母为键，选项内容为值"
          }
        ],
        "role": "user"
      }
    ]
  },
  "model": "qwen-vl-plus",
  "parameters": {
    "presence_penalty": 1,
    "repetition_penalty": 1,
    "result_format": "json_object",
    "temperature": 0.2,
    "top_k": 1,
    "top_p": 0.01
  }
}
2025/06/18 12:43:45.571898 model_service.go:243: 🔍 [HTTP-REQUEST] 请求头 Content-Type: application/json
2025/06/18 12:43:45.571921 model_service.go:244: 🔍 [HTTP-REQUEST] 请求头 Authorization: Bearer sk-21d8cdf***
2025/06/18 12:43:47.731699 model_service.go:265: 🔍 [HTTP-RESPONSE] 响应状态码: 200
2025/06/18 12:43:47.731844 model_service.go:266: 🔍 [HTTP-RESPONSE] 响应体长度: 755 bytes
2025/06/18 12:43:47.731894 model_service.go:267: 🔍 [HTTP-RESPONSE] 响应头 Content-Type: application/json
2025/06/18 12:43:47.731959 model_service.go:272: 🔍 [HTTP-RESPONSE] 完整响应体:
{
  "output": {
    "choices": [
      {
        "finish_reason": "stop",
        "message": {
          "role": "assistant",
          "content": [
            {
              "text": "```json\n{\n    \"qutext\": \"(单选题)14、如图所示，驾车遇到此情况时应当注意什么？\",\n    \"options\": {\n        \"A\": \"左侧A柱盲区内可能有行人将要通过\",\n        \"B\": \"对向车道车辆将要调头\",\n        \"C\": \"后面有车辆将超车\",\n        \"D\": \"右侧车道有车辆将要通过\"\n    }\n}\n```"
            }
          ]
        }
      }
    ]
  },
  "usage": {
    "input_tokens_details": {
      "text_tokens": 72,
      "image_tokens": 860
    },
    "total_tokens": 1030,
    "output_tokens": 98,
    "input_tokens": 932,
    "output_tokens_details": {
      "text_tokens": 98
    },
    "image_tokens": 860,
    "prompt_tokens_details": {
      "cached_tokens": 0
    }
  },
  "request_id": "84487fd9-4396-9448-bfbd-1579cbc8c0e8"
}
2025/06/18 12:43:47.732434 model_service.go:284: ✅ [HTTP-RESPONSE] JSON解析成功
2025/06/18 12:43:47.732506 solve_service.go:178: ✅ [OCR] 模型调用成功 - Token消耗: 1030
2025/06/18 12:43:47.732541 solve_service.go:184: 📝 [OCR] 开始解析OCR响应
2025/06/18 12:43:47.732566 parser_service.go:30: 🔍 [OCR-DEBUG] Content类型: []interface {}
2025/06/18 12:43:47.732592 parser_service.go:39: 🔍 [OCR-DEBUG] Array类型，元素数量: 1
2025/06/18 12:43:47.732616 parser_service.go:42: 🔍 [OCR-DEBUG] Array[0]类型: map[string]interface {}
2025/06/18 12:43:47.732648 parser_service.go:47: 🔍 [OCR-DEBUG] 提取文本: ```json
{
    "qutext": "(单选题)14、如图所示，驾车遇到此情况时应当注意什么？",
    "options": {
        "A": "左侧A柱盲区内可能有行人将要通过",
        "B": "对向车道车辆将要调头",
        "C": "后面有车辆将超车",
        "D": "右侧车道有车辆将要通过"
    }
}
```
2025/06/18 12:43:47.732724 parser_service.go:59: 🔍 [OCR-DEBUG] 最终内容长度: 332
2025/06/18 12:43:47.732751 parser_service.go:60: 🔍 [OCR-DEBUG] 最终内容前200字符: ```json
{
    "qutext": "(单选题)14、如图所示，驾车遇到此情况时应当注意什么？",
    "options": {
        "A": "左侧A柱盲区内可能有行人将要通过",
        "B": "对向车道车辆将要调头",
        "C": "后面有车辆将超车",
        "D": "右侧车道有车辆将要通过"
    }
}
```
2025/06/18 12:43:47.732779 parser_service.go:62: 🔍 [OCR-DEBUG] 最终内容后200字符: : "左侧A柱盲区内可能有行人将要通过",
        "B": "对向车道车辆将要调头",
        "C": "后面有车辆将超车",
        "D": "右侧车道有车辆将要通过"
    }
}
```
2025/06/18 12:43:47.732845 parser_service.go:297: 🔍 [JSON-EXTRACT] 开始提取JSON，原始内容: ```json
{
    "qutext": "(单选题)14、如图所示，驾车遇到此情况时应当注意什么？",
    "options": {
        "A": "左侧A柱盲区内可能有行人将要通过",
        "B": "对向车道车辆将要调头",
        "C": "后面有车辆将超车",
        "D": "右侧车道有车辆将要通过"
    }
}
```
2025/06/18 12:43:47.732930 parser_service.go:308: 🔍 [JSON-EXTRACT] 从```json```中提取: {
    "qutext": "(单选题)14、如图所示，驾车遇到此情况时应当注意什么？",
    "options": {
        "A": "左侧A柱盲区内可能有行人将要通过",
        "B": "对向车道车辆将要调头",
        "C": "后面有车辆将超车",
        "D": "右侧车道有车辆将要通过"
    }
}
2025/06/18 12:43:47.732963 parser_service.go:338: 🔍 [FIELD-MAP] 开始字段映射，原始JSON: {
    "qutext": "(单选题)14、如图所示，驾车遇到此情况时应当注意什么？",
    "options": {
        "A": "左侧A柱盲区内可能有行人将要通过",
        "B": "对向车道车辆将要调头",
        "C": "后面有车辆将超车",
        "D": "右侧车道有车辆将要通过"
    }
}
2025/06/18 12:43:47.732996 parser_service.go:343: 🔍 [FIELD-MAP] 映射完成，结果: {
    "qutext": "(单选题)14、如图所示，驾车遇到此情况时应当注意什么？",
    "options": {
        "A": "左侧A柱盲区内可能有行人将要通过",
        "B": "对向车道车辆将要调头",
        "C": "后面有车辆将超车",
        "D": "右侧车道有车辆将要通过"
    }
}
2025/06/18 12:43:47.733023 parser_service.go:70: 🔍 [OCR-DEBUG] 字段映射后的JSON: {
    "qutext": "(单选题)14、如图所示，驾车遇到此情况时应当注意什么？",
    "options": {
        "A": "左侧A柱盲区内可能有行人将要通过",
        "B": "对向车道车辆将要调头",
        "C": "后面有车辆将超车",
        "D": "右侧车道有车辆将要通过"
    }
}
2025/06/18 12:43:47.733089 parser_service.go:80: ✅ [OCR-PARSE] JSON解析成功
2025/06/18 12:43:47.733127 parser_service.go:81: 📋 [OCR-PARSE] 解析结果 - QuText: (单选题)14、如图所示，驾车遇到此情况时应当注意什么？
2025/06/18 12:43:47.733152 parser_service.go:82: 📋 [OCR-PARSE] 解析结果 - Options数量: 4
2025/06/18 12:43:47.733178 parser_service.go:84: 📋 [OCR-PARSE] 选项 D: 右侧车道有车辆将要通过
2025/06/18 12:43:47.733204 parser_service.go:84: 📋 [OCR-PARSE] 选项 A: 左侧A柱盲区内可能有行人将要通过
2025/06/18 12:43:47.733227 parser_service.go:84: 📋 [OCR-PARSE] 选项 B: 对向车道车辆将要调头
2025/06/18 12:43:47.733247 parser_service.go:84: 📋 [OCR-PARSE] 选项 C: 后面有车辆将超车
2025/06/18 12:43:47.733266 parser_service.go:88: 🔍 [TYPE-DETECT] 开始检测题目类型，题干内容: (单选题)14、如图所示，驾车遇到此情况时应当注意什么？
2025/06/18 12:43:47.733326 parser_service.go:169: 🔍 [TYPE-DETECT] 原始内容: (单选题)14、如图所示，驾车遇到此情况时应当注意什么？
2025/06/18 12:43:47.733404 parser_service.go:171: 🔍 [TYPE-DETECT] 转换小写后: (单选题)14、如图所示，驾车遇到此情况时应当注意什么？
2025/06/18 12:43:47.733424 parser_service.go:174: 🔍 [TYPE-DETECT] 检测到单选题
2025/06/18 12:43:47.733446 parser_service.go:90: 🔍 [TYPE-DETECT] 检测结果: 单选题
2025/06/18 12:43:47.733467 parser_service.go:97: 🧹 [DATA-CLEAN] 开始清洗题干内容（二次提纯）
2025/06/18 12:43:47.733488 parser_service.go:98: 🧹 [DATA-CLEAN] 原始题干: (单选题)14、如图所示，驾车遇到此情况时应当注意什么？
2025/06/18 12:43:47.733618 parser_service.go:208: 🧹 [DATA-CLEAN] 第一步清除前缀后: 如图所示，驾车遇到此情况时应当注意什么？
2025/06/18 12:43:47.733692 parser_service.go:213: 🧹 [DATA-CLEAN] 第二步清理标点后: 如图所示驾车遇到此情况时应当注意什么
2025/06/18 12:43:47.733732 parser_service.go:100: 🧹 [DATA-CLEAN] 二次提纯完成，最终题干: 如图所示驾车遇到此情况时应当注意什么
2025/06/18 12:43:47.733762 parser_service.go:103: 🧹 [DATA-CLEAN] 开始清洗选项内容
2025/06/18 12:43:47.733811 parser_service.go:227: 🧹 [DATA-CLEAN] 选项 A 清理标点前: 左侧A柱盲区内可能有行人将要通过, 清理后: 左侧A柱盲区内可能有行人将要通过
2025/06/18 12:43:47.733853 parser_service.go:227: 🧹 [DATA-CLEAN] 选项 B 清理标点前: 对向车道车辆将要调头, 清理后: 对向车道车辆将要调头
2025/06/18 12:43:47.733885 parser_service.go:227: 🧹 [DATA-CLEAN] 选项 C 清理标点前: 后面有车辆将超车, 清理后: 后面有车辆将超车
2025/06/18 12:43:47.733914 parser_service.go:227: 🧹 [DATA-CLEAN] 选项 D 清理标点前: 右侧车道有车辆将要通过, 清理后: 右侧车道有车辆将要通过
2025/06/18 12:43:47.733940 parser_service.go:106: 🧹 [DATA-CLEAN] 清洗后选项 A: 左侧A柱盲区内可能有行人将要通过
2025/06/18 12:43:47.733968 parser_service.go:106: 🧹 [DATA-CLEAN] 清洗后选项 B: 对向车道车辆将要调头
2025/06/18 12:43:47.733993 parser_service.go:106: 🧹 [DATA-CLEAN] 清洗后选项 C: 后面有车辆将超车
2025/06/18 12:43:47.734015 parser_service.go:106: 🧹 [DATA-CLEAN] 清洗后选项 D: 右侧车道有车辆将要通过
2025/06/18 12:43:47.734047 parser_service.go:111: 📏 [DATA-CALC] 题干字符长度: 18
2025/06/18 12:43:47.734101 parser_service.go:115: 🔑 [DATA-CALC] 生成哈希键: 06b2720385c5e4c0df2405cbcbf815ab
2025/06/18 12:43:47.734186 parser_service.go:199: 🧹 [DATA-CLEAN] 清除前缀后: 如图所示，驾车遇到此情况时应当注意什么？
2025/06/18 12:43:47.734235 parser_service.go:132: 📦 [SAVE-QUESTION] 构建完成
2025/06/18 12:43:47.734272 parser_service.go:133: 📦 [SAVE-QUESTION] Type: 单选题
2025/06/18 12:43:47.734311 parser_service.go:134: 📦 [SAVE-QUESTION] Content: 如图所示，驾车遇到此情况时应当注意什么？
2025/06/18 12:43:47.734571 parser_service.go:135: 📦 [SAVE-QUESTION] CleanContent: 如图所示驾车遇到此情况时应当注意什么
2025/06/18 12:43:47.734624 parser_service.go:136: 📦 [SAVE-QUESTION] UserURL: http://img.igmdns.com/images/cc0001.jpg
2025/06/18 12:43:47.734663 parser_service.go:137: 📦 [SAVE-QUESTION] QuestionLen: 18
2025/06/18 12:43:47.734699 parser_service.go:138: 📦 [SAVE-QUESTION] HashKey: 06b2720385c5e4c0df2405cbcbf815ab
2025/06/18 12:43:47.734738 parser_service.go:139: 📦 [SAVE-QUESTION] Options数量: 4
2025/06/18 12:43:47.734775 solve_service.go:190: ✅ [OCR] 响应解析成功 - 题目类型: 单选题, 内容长度: 60
2025/06/18 12:43:47.734805 solve_service.go:194: 🔄 [CONTEXT] ProcessContext已更新
2025/06/18 12:43:47.734830 solve_service.go:195: 🔄 [CONTEXT] SaveQuestion.Type: 单选题
2025/06/18 12:43:47.734852 solve_service.go:196: 🔄 [CONTEXT] SaveQuestion.HashKey: 06b2720385c5e4c0df2405cbcbf815ab
2025/06/18 12:43:47.734913 solve_service.go:197: 🔄 [CONTEXT] SaveQuestion.QuestionLen: 18
2025/06/18 12:43:47.734940 solve_service.go:198: 🔄 [CONTEXT] SaveQuestion.UserURL: http://img.igmdns.com/images/cc0001.jpg
2025/06/18 12:43:47.734964 solve_service.go:199: 🔄 [CONTEXT] OCRToken: 1030
2025/06/18 12:43:47.734989 solve_service.go:200: 🔄 [CONTEXT] 当前处理阶段: OCR解析完成，准备进入缓存检查阶段
2025/06/18 12:43:47.735013 solve_service.go:87: ✅ [SOLVE] OCR处理成功 - Token消耗: 1030, 题目类型: 单选题
2025/06/18 12:43:47.735042 solve_service.go:90: 🔄 [SOLVE] 步骤4: 检查Redis缓存
2025/06/18 12:43:47.735069 solve_service.go:207: 🔍 [CACHE-CHECK] 开始检查Redis缓存
2025/06/18 12:43:47.735100 solve_service.go:208: 🔍 [CACHE-CHECK] 使用HashKey: 06b2720385c5e4c0df2405cbcbf815ab
2025/06/18 12:43:47.771588 solve_service.go:212: ⚪ [CACHE-CHECK] Redis缓存未命中 - Error: <nil>, Questions数量: 0
2025/06/18 12:43:47.771685 solve_service.go:213: 🔄 [CONTEXT] 缓存检查结果: 未命中，继续下一步
2025/06/18 12:43:47.771766 solve_service.go:95: ⚪ [SOLVE] Redis缓存未命中
2025/06/18 12:43:47.771791 solve_service.go:98: 🎯 [SOLVE] 步骤5: 检查MySQL精确匹配
2025/06/18 12:43:47.771807 solve_service.go:235: 🔍 [MYSQL-EXACT] 开始MySQL精确匹配
2025/06/18 12:43:47.771822 solve_service.go:236: 🔍 [MYSQL-EXACT] 使用HashKey: 06b2720385c5e4c0df2405cbcbf815ab

2025/06/18 12:43:47 [32m/Users/<USER>/Documents/Project/Solve/solve-go-api/internal/services/question_service.go:23
[0m[33m[59.595ms] [34;1m[rows:0][0m SELECT * FROM `hook_question_bank` WHERE hash_key = '06b2720385c5e4c0df2405cbcbf815ab'
2025/06/18 12:43:47.831568 solve_service.go:240: ⚪ [MYSQL-EXACT] MySQL精确匹配未命中 - Error: <nil>, Questions数量: 0
2025/06/18 12:43:47.831653 solve_service.go:241: 🔄 [CONTEXT] 精确匹配结果: 未命中，继续模糊匹配
2025/06/18 12:43:47.831700 solve_service.go:103: ⚪ [SOLVE] MySQL精确匹配未命中
2025/06/18 12:43:47.831750 solve_service.go:106: 🔍 [SOLVE] 步骤6: 检查MySQL模糊匹配
2025/06/18 12:43:47.831791 solve_service.go:273: 🔍 [MYSQL-FUZZY] 开始MySQL模糊匹配
2025/06/18 12:43:47.831827 solve_service.go:274: 🔍 [MYSQL-FUZZY] 题目类型: 单选题
2025/06/18 12:43:47.831860 solve_service.go:275: 🔍 [MYSQL-FUZZY] 清洗后内容: 如图所示驾车遇到此情况时应当注意什么
2025/06/18 12:43:47.831888 solve_service.go:276: 🔍 [MYSQL-FUZZY] 题目长度: 18
2025/06/18 12:43:47.831917 match_service.go:30: 🔍 [FUZZY-MATCH] 开始三步筛选匹配

2025/06/18 12:43:47 [32m/Users/<USER>/Documents/Project/Solve/solve-go-api/internal/services/match_service.go:41
[0m[33m[59.496ms] [34;1m[rows:2][0m SELECT * FROM `hook_question_bank` WHERE verified = true AND type = 2 AND ABS(question_len - 18) <= 5
2025/06/18 12:43:47.891647 match_service.go:47: 🔍 [STEP-1] MySQL筛选完成 - 找到2个候选题目
2025/06/18 12:43:47.891725 match_service.go:84: 🔍 [EDIT-DISTANCE] 开始计算2个候选题目的相似度
2025/06/18 12:43:47.891778 match_service.go:89: 🔍 [EDIT-DISTANCE] 候选1 - 相似度: 1.000, 内容: 如图所示驾车遇到此情况时应当注意什么
2025/06/18 12:43:47.891828 match_service.go:89: 🔍 [EDIT-DISTANCE] 候选2 - 相似度: 0.947, 内容: 如图所示啊驾车遇到此情况时应当注意什么
2025/06/18 12:43:47.891866 match_service.go:109: 🔍 [EDIT-DISTANCE] 选中候选1 - 相似度: 1.000
2025/06/18 12:43:47.891892 match_service.go:109: 🔍 [EDIT-DISTANCE] 选中候选2 - 相似度: 0.947
2025/06/18 12:43:47.891914 match_service.go:54: 🔍 [STEP-2] 编辑距离筛选完成 - 取前3个最相似的题目
2025/06/18 12:43:47.891968 match_service.go:61: 🔍 [STEP-3] 选项交集筛选完成 - 最终匹配1个题目
2025/06/18 12:43:47.892010 solve_service.go:286: ✅ [MYSQL-FUZZY] MySQL模糊匹配命中 - 找到1个相似题目
2025/06/18 12:43:47.892034 solve_service.go:288: ✅ [MYSQL-FUZZY] 相似题目1 - ID: 1, Type: 2, Content: 如图所示，驾车遇到此情况时应当注意什么？
2025/06/18 12:43:47.892166 solve_service.go:297: 🔄 [CONTEXT] ProcessContext已更新 - MySQL模糊匹配命中
2025/06/18 12:43:47.892209 solve_service.go:298: 🔄 [CONTEXT] Source: match
2025/06/18 12:43:47.892234 solve_service.go:299: 🔄 [CONTEXT] Status: 1
2025/06/18 12:43:47.892258 solve_service.go:300: 🔄 [CONTEXT] Message: success
2025/06/18 12:43:47.892276 solve_service.go:301: 🔄 [CONTEXT] Response长度: 673 bytes
2025/06/18 12:43:47.892325 solve_service.go:302: 🔄 [CONTEXT] 处理完成: MySQL模糊匹配命中，直接返回结果
2025/06/18 12:43:47.892340 solve_service.go:108: ✅ [SOLVE] MySQL模糊匹配成功
2025/06/18 12:43:47.892357 solve_handler.go:74: 📝 [HANDLER] 记录解题日志

2025/06/18 12:43:48 [32m/Users/<USER>/Documents/Project/Solve/solve-go-api/internal/services/log_service.go:41
[0m[33m[128.839ms] [34;1m[rows:1][0m INSERT INTO `hook_solve_logs` (`app_id`,`user_url`,`matched_id`,`ocr_token`,`source`,`data`,`status`,`latency`,`created_at`) VALUES ('NdOCNqqWPtrLFvZj','http://img.igmdns.com/images/cc0001.jpg',0,1030,'match','[{"id":1,"type":"单选题","content":"如图所示，驾车遇到此情况时应当注意什么？","options":{"A":"左侧A柱盲区内可能有行人将要通过","B":"对向车道车1辆将要调头","C":"后面有车辆将超车","D":"右侧车道有车辆将要通过"},"answer":{"A":"左侧A柱盲区内可能有行人将要通过"},"analysis":"在驾驶过程中，遇到题目描述的情况时，需要特别注意A柱盲区，因为盲区内可能有行人或其他障碍物无法直接观察到，而其他选项相对而言不是最需要优先注意的内容。因此，正确答案为A。","image_url":"","user_url":"http://img.igmdns.com/images/cc0001.jpg"}]',1,2500,'2025-06-18 12:43:47.922')
2025/06/18 12:43:48.021511 solve_handler.go:83: 🎉 [HANDLER] 解题处理成功

2025/06/18 12:43:48 [32m/Users/<USER>/Documents/Project/Solve/solve-go-api/internal/services/app_service.go:105
[0m[33m[129.540ms] [34;1m[rows:1][0m UPDATE `hook_apps` SET `total_calls`=total_calls + 1 WHERE app_id = 'NdOCNqqWPtrLFvZj'

2025/06/18 12:43:48 [32m/Users/<USER>/Documents/Project/Solve/solve-go-api/internal/services/app_service.go:123
[0m[33m[59.932ms] [34;1m[rows:1][0m UPDATE `hook_user` SET `balance`=balance - 2060,`updated_at`='2025-06-18 12:43:48.182' WHERE id = 20

2025/06/18 12:43:48 [32m/Users/<USER>/Documents/Project/Solve/solve-go-api/internal/services/app_service.go:136
[0m[33m[59.835ms] [34;1m[rows:1][0m INSERT INTO `hook_balance_logs` (`user_id`,`change_amount`,`reason`,`operator_id`,`created_at`) VALUES (20,-2060,'API调用扣费',0,'2025-06-18 12:43:48.242')
time="2025-06-18T12:43:48+08:00" level=info msg="HTTP Request" body_size=713 client_ip="::1" error= latency=3.073250083s method=POST path=/api/v1/solve/question status_code=200 timestamp="2025-06-18T12:43:48+08:00" user_agent=PostmanRuntime-ApipostRuntime/1.1.0
[GIN] 2025/06/18 - 12:43:48 | 200 |  3.073455792s |             ::1 | POST     "/api/v1/solve/question"

2025/06/18 12:44:09 [32m/Users/<USER>/Documents/Project/Solve/solve-go-api/internal/services/app_service.go:95
[0m[33m[67.315ms] [34;1m[rows:1][0m SELECT * FROM `hook_apps` WHERE app_id = 'NdOCNqqWPtrLFvZj' AND app_secret = 'VsubbUlgZejHgRrylJ0qs3A9HJy3a8P2' ORDER BY `hook_apps`.`id` LIMIT 1

2025/06/18 12:44:09 [32m/Users/<USER>/Documents/Project/Solve/solve-go-api/internal/services/app_service.go:111
[0m[33m[60.100ms] [34;1m[rows:1][0m SELECT * FROM `hook_user` WHERE `hook_user`.`id` = 20 ORDER BY `hook_user`.`id` LIMIT 1
2025/06/18 12:44:09.761716 solve_handler.go:31: 🎯 [HANDLER] 收到解题请求 - IP: ::1, UserAgent: PostmanRuntime-ApipostRuntime/1.1.0
2025/06/18 12:44:09.761835 solve_handler.go:63: ✅ [HANDLER] 请求参数解析成功 - AppID: NdOCNqqWPtrLFvZj, ImageURL: http://img.igmdns.com/images/cc0002.jpg
2025/06/18 12:44:09.761886 solve_handler.go:66: 🚀 [HANDLER] 开始处理解题请求
2025/06/18 12:44:09.761926 solve_service.go:61: 🚀 [SOLVE] 开始处理解题请求 - AppID: NdOCNqqWPtrLFvZj, ImageURL: http://img.igmdns.com/images/cc0002.jpg
2025/06/18 12:44:09.761958 solve_service.go:64: 📋 [SOLVE] 步骤1: 验证应用和用户

2025/06/18 12:44:09 [32m/Users/<USER>/Documents/Project/Solve/solve-go-api/internal/services/app_service.go:53
[0m[33m[59.578ms] [34;1m[rows:1][0m SELECT * FROM `hook_apps` WHERE app_id = 'NdOCNqqWPtrLFvZj' ORDER BY `hook_apps`.`id` LIMIT 1

2025/06/18 12:44:09 [32m/Users/<USER>/Documents/Project/Solve/solve-go-api/internal/services/app_service.go:111
[0m[33m[59.841ms] [34;1m[rows:1][0m SELECT * FROM `hook_user` WHERE `hook_user`.`id` = 20 ORDER BY `hook_user`.`id` LIMIT 1
2025/06/18 12:44:09.881764 solve_service.go:70: ✅ [SOLVE] 应用/用户验证成功 - UserID: 20, Balance: 467040
2025/06/18 12:44:09.881835 solve_service.go:73: 💰 [SOLVE] 步骤2: 检查用户积分 - 当前积分: 467040
2025/06/18 12:44:09.881931 solve_service.go:81: 🔍 [SOLVE] 步骤3: 调用OCR模型识别图片
2025/06/18 12:44:09.881964 solve_service.go:170: 🔍 [OCR] 开始OCR处理 - 模型: qwen-vl-plus, 图片URL: http://img.igmdns.com/images/cc0002.jpg

2025/06/18 12:44:09 [32m/Users/<USER>/Documents/Project/Solve/solve-go-api/internal/services/model_service.go:39
[0m[33m[59.895ms] [34;1m[rows:1][0m SELECT * FROM `hook_models` WHERE model_name = 'qwen-vl-plus' ORDER BY `hook_models`.`id` LIMIT 1
2025/06/18 12:44:09.942147 model_service.go:217: 🔍 [HTTP-REQUEST] 模型: qwen-vl-plus
2025/06/18 12:44:09.942250 model_service.go:218: 🔍 [HTTP-REQUEST] 请求URL: https://dashscope.aliyuncs.com/api/v1/services/aigc/multimodal-generation/generation
2025/06/18 12:44:09.942296 model_service.go:219: 🔍 [HTTP-REQUEST] 请求体长度: 573 bytes
2025/06/18 12:44:09.942359 model_service.go:224: 🔍 [HTTP-REQUEST] 完整请求体:
{
  "input": {
    "messages": [
      {
        "content": "严格标准返回json格式。示例{\"qutext\":\"(题目类型)(序号)(题目正文)\",\"options\":{\"所有选项\"}}",
        "role": "system"
      },
      {
        "content": [
          {
            "image": "http://img.igmdns.com/images/cc0002.jpg"
          },
          {
            "text": "精准且完整的识别问题,严格按照要求输出的完整json,其中的options字段的格式必须正确,选项字母为键，选项内容为值"
          }
        ],
        "role": "user"
      }
    ]
  },
  "model": "qwen-vl-plus",
  "parameters": {
    "presence_penalty": 1,
    "repetition_penalty": 1,
    "result_format": "json_object",
    "temperature": 0.2,
    "top_k": 1,
    "top_p": 0.01
  }
}
2025/06/18 12:44:09.942446 model_service.go:243: 🔍 [HTTP-REQUEST] 请求头 Content-Type: application/json
2025/06/18 12:44:09.942489 model_service.go:244: 🔍 [HTTP-REQUEST] 请求头 Authorization: Bearer sk-21d8cdf***
2025/06/18 12:44:12.741986 model_service.go:265: 🔍 [HTTP-RESPONSE] 响应状态码: 200
2025/06/18 12:44:12.742156 model_service.go:266: 🔍 [HTTP-RESPONSE] 响应体长度: 819 bytes
2025/06/18 12:44:12.742214 model_service.go:267: 🔍 [HTTP-RESPONSE] 响应头 Content-Type: application/json
2025/06/18 12:44:12.742298 model_service.go:272: 🔍 [HTTP-RESPONSE] 完整响应体:
{
  "output": {
    "choices": [
      {
        "finish_reason": "stop",
        "message": {
          "role": "assistant",
          "content": [
            {
              "text": "```json\n{\n    \"qutext\": \"(单选题)18、如图所示，驾驶机动车遇到这种情况时，以下做法正确的是？\",\n    \"options\": {\n        \"A\": \"应减速观察水情，然后加速行驶通过\",\n        \"B\": \"可随意通行\",\n        \"C\": \"应停车察明水情，确认安全后，快速通过\",\n        \"D\": \"应停车察明水情，确认安全后，低速通过\"\n    }\n}\n```"
            }
          ]
        }
      }
    ]
  },
  "usage": {
    "input_tokens_details": {
      "text_tokens": 72,
      "image_tokens": 1232
    },
    "total_tokens": 1412,
    "output_tokens": 108,
    "input_tokens": 1304,
    "output_tokens_details": {
      "text_tokens": 108
    },
    "image_tokens": 1232,
    "prompt_tokens_details": {
      "cached_tokens": 0
    }
  },
  "request_id": "5d4e17db-e80a-9231-8b2a-78dd6db0c2e6"
}
2025/06/18 12:44:12.742475 model_service.go:284: ✅ [HTTP-RESPONSE] JSON解析成功
2025/06/18 12:44:12.742542 solve_service.go:178: ✅ [OCR] 模型调用成功 - Token消耗: 1412
2025/06/18 12:44:12.742583 solve_service.go:184: 📝 [OCR] 开始解析OCR响应
2025/06/18 12:44:12.742610 parser_service.go:30: 🔍 [OCR-DEBUG] Content类型: []interface {}
2025/06/18 12:44:12.742638 parser_service.go:39: 🔍 [OCR-DEBUG] Array类型，元素数量: 1
2025/06/18 12:44:12.742662 parser_service.go:42: 🔍 [OCR-DEBUG] Array[0]类型: map[string]interface {}
2025/06/18 12:44:12.742694 parser_service.go:47: 🔍 [OCR-DEBUG] 提取文本: ```json
{
    "qutext": "(单选题)18、如图所示，驾驶机动车遇到这种情况时，以下做法正确的是？",
    "options": {
        "A": "应减速观察水情，然后加速行驶通过",
        "B": "可随意通行",
        "C": "应停车察明水情，确认安全后，快速通过",
        "D": "应停车察明水情，确认安全后，低速通过"
    }
}
```
2025/06/18 12:44:12.742738 parser_service.go:59: 🔍 [OCR-DEBUG] 最终内容长度: 391
2025/06/18 12:44:12.742763 parser_service.go:60: 🔍 [OCR-DEBUG] 最终内容前200字符: ```json
{
    "qutext": "(单选题)18、如图所示，驾驶机动车遇到这种情况时，以下做法正确的是？",
    "options": {
        "A": "应减速观察水情，然后加速行驶通过",
        "B": "可随意通行",
        "C": "应停车察明水情，确认安全后，快速通过",
        "D": "应停车察明水情，确认安全后，低速
2025/06/18 12:44:12.742790 parser_service.go:62: 🔍 [OCR-DEBUG] 最终内容后200字符: �行驶通过",
        "B": "可随意通行",
        "C": "应停车察明水情，确认安全后，快速通过",
        "D": "应停车察明水情，确认安全后，低速通过"
    }
}
```
2025/06/18 12:44:12.742815 parser_service.go:297: 🔍 [JSON-EXTRACT] 开始提取JSON，原始内容: ```json
{
    "qutext": "(单选题)18、如图所示，驾驶机动车遇到这种情况时，以下做法正确的是？",
    "options": {
        "A": "应减速观察水情，然后加速行驶通过",
        "B": "可随意通行",
        "C": "应停车察明水情，确认安全后，快速通过",
        "D": "应停车察明水情，确认安全后，低速通过"
    }
}
```
2025/06/18 12:44:12.742841 parser_service.go:308: 🔍 [JSON-EXTRACT] 从```json```中提取: {
    "qutext": "(单选题)18、如图所示，驾驶机动车遇到这种情况时，以下做法正确的是？",
    "options": {
        "A": "应减速观察水情，然后加速行驶通过",
        "B": "可随意通行",
        "C": "应停车察明水情，确认安全后，快速通过",
        "D": "应停车察明水情，确认安全后，低速通过"
    }
}
2025/06/18 12:44:12.742866 parser_service.go:338: 🔍 [FIELD-MAP] 开始字段映射，原始JSON: {
    "qutext": "(单选题)18、如图所示，驾驶机动车遇到这种情况时，以下做法正确的是？",
    "options": {
        "A": "应减速观察水情，然后加速行驶通过",
        "B": "可随意通行",
        "C": "应停车察明水情，确认安全后，快速通过",
        "D": "应停车察明水情，确认安全后，低速通过"
    }
}
2025/06/18 12:44:12.742892 parser_service.go:343: 🔍 [FIELD-MAP] 映射完成，结果: {
    "qutext": "(单选题)18、如图所示，驾驶机动车遇到这种情况时，以下做法正确的是？",
    "options": {
        "A": "应减速观察水情，然后加速行驶通过",
        "B": "可随意通行",
        "C": "应停车察明水情，确认安全后，快速通过",
        "D": "应停车察明水情，确认安全后，低速通过"
    }
}
2025/06/18 12:44:12.742943 parser_service.go:70: 🔍 [OCR-DEBUG] 字段映射后的JSON: {
    "qutext": "(单选题)18、如图所示，驾驶机动车遇到这种情况时，以下做法正确的是？",
    "options": {
        "A": "应减速观察水情，然后加速行驶通过",
        "B": "可随意通行",
        "C": "应停车察明水情，确认安全后，快速通过",
        "D": "应停车察明水情，确认安全后，低速通过"
    }
}
2025/06/18 12:44:12.743000 parser_service.go:80: ✅ [OCR-PARSE] JSON解析成功
2025/06/18 12:44:12.743032 parser_service.go:81: 📋 [OCR-PARSE] 解析结果 - QuText: (单选题)18、如图所示，驾驶机动车遇到这种情况时，以下做法正确的是？
2025/06/18 12:44:12.743056 parser_service.go:82: 📋 [OCR-PARSE] 解析结果 - Options数量: 4
2025/06/18 12:44:12.743080 parser_service.go:84: 📋 [OCR-PARSE] 选项 A: 应减速观察水情，然后加速行驶通过
2025/06/18 12:44:12.743104 parser_service.go:84: 📋 [OCR-PARSE] 选项 B: 可随意通行
2025/06/18 12:44:12.743127 parser_service.go:84: 📋 [OCR-PARSE] 选项 C: 应停车察明水情，确认安全后，快速通过
2025/06/18 12:44:12.743149 parser_service.go:84: 📋 [OCR-PARSE] 选项 D: 应停车察明水情，确认安全后，低速通过
2025/06/18 12:44:12.743172 parser_service.go:88: 🔍 [TYPE-DETECT] 开始检测题目类型，题干内容: (单选题)18、如图所示，驾驶机动车遇到这种情况时，以下做法正确的是？
2025/06/18 12:44:12.743195 parser_service.go:169: 🔍 [TYPE-DETECT] 原始内容: (单选题)18、如图所示，驾驶机动车遇到这种情况时，以下做法正确的是？
2025/06/18 12:44:12.743222 parser_service.go:171: 🔍 [TYPE-DETECT] 转换小写后: (单选题)18、如图所示，驾驶机动车遇到这种情况时，以下做法正确的是？
2025/06/18 12:44:12.743244 parser_service.go:174: 🔍 [TYPE-DETECT] 检测到单选题
2025/06/18 12:44:12.743269 parser_service.go:90: 🔍 [TYPE-DETECT] 检测结果: 单选题
2025/06/18 12:44:12.743294 parser_service.go:97: 🧹 [DATA-CLEAN] 开始清洗题干内容（二次提纯）
2025/06/18 12:44:12.743318 parser_service.go:98: 🧹 [DATA-CLEAN] 原始题干: (单选题)18、如图所示，驾驶机动车遇到这种情况时，以下做法正确的是？
2025/06/18 12:44:12.743450 parser_service.go:208: 🧹 [DATA-CLEAN] 第一步清除前缀后: 如图所示，驾驶机动车遇到这种情况时，以下做法正确的是？
2025/06/18 12:44:12.743540 parser_service.go:213: 🧹 [DATA-CLEAN] 第二步清理标点后: 如图所示驾驶机动车遇到这种情况时以下做法正确的是
2025/06/18 12:44:12.743576 parser_service.go:100: 🧹 [DATA-CLEAN] 二次提纯完成，最终题干: 如图所示驾驶机动车遇到这种情况时以下做法正确的是
2025/06/18 12:44:12.743601 parser_service.go:103: 🧹 [DATA-CLEAN] 开始清洗选项内容
2025/06/18 12:44:12.743639 parser_service.go:227: 🧹 [DATA-CLEAN] 选项 B 清理标点前: 可随意通行, 清理后: 可随意通行
2025/06/18 12:44:12.743675 parser_service.go:227: 🧹 [DATA-CLEAN] 选项 C 清理标点前: 应停车察明水情，确认安全后，快速通过, 清理后: 应停车察明水情确认安全后快速通过
2025/06/18 12:44:12.743703 parser_service.go:227: 🧹 [DATA-CLEAN] 选项 D 清理标点前: 应停车察明水情，确认安全后，低速通过, 清理后: 应停车察明水情确认安全后低速通过
2025/06/18 12:44:12.743730 parser_service.go:227: 🧹 [DATA-CLEAN] 选项 A 清理标点前: 应减速观察水情，然后加速行驶通过, 清理后: 应减速观察水情然后加速行驶通过
2025/06/18 12:44:12.743751 parser_service.go:106: 🧹 [DATA-CLEAN] 清洗后选项 B: 可随意通行
2025/06/18 12:44:12.743776 parser_service.go:106: 🧹 [DATA-CLEAN] 清洗后选项 C: 应停车察明水情确认安全后快速通过
2025/06/18 12:44:12.743798 parser_service.go:106: 🧹 [DATA-CLEAN] 清洗后选项 D: 应停车察明水情确认安全后低速通过
2025/06/18 12:44:12.743816 parser_service.go:106: 🧹 [DATA-CLEAN] 清洗后选项 A: 应减速观察水情然后加速行驶通过
2025/06/18 12:44:12.743834 parser_service.go:111: 📏 [DATA-CALC] 题干字符长度: 24
2025/06/18 12:44:12.743870 parser_service.go:115: 🔑 [DATA-CALC] 生成哈希键: 3bd29cb801b86bfaf2eb23001bd7ca83
2025/06/18 12:44:12.743935 parser_service.go:199: 🧹 [DATA-CLEAN] 清除前缀后: 如图所示，驾驶机动车遇到这种情况时，以下做法正确的是？
2025/06/18 12:44:12.743964 parser_service.go:132: 📦 [SAVE-QUESTION] 构建完成
2025/06/18 12:44:12.743990 parser_service.go:133: 📦 [SAVE-QUESTION] Type: 单选题
2025/06/18 12:44:12.744014 parser_service.go:134: 📦 [SAVE-QUESTION] Content: 如图所示，驾驶机动车遇到这种情况时，以下做法正确的是？
2025/06/18 12:44:12.744039 parser_service.go:135: 📦 [SAVE-QUESTION] CleanContent: 如图所示驾驶机动车遇到这种情况时以下做法正确的是
2025/06/18 12:44:12.744064 parser_service.go:136: 📦 [SAVE-QUESTION] UserURL: http://img.igmdns.com/images/cc0002.jpg
2025/06/18 12:44:12.744089 parser_service.go:137: 📦 [SAVE-QUESTION] QuestionLen: 24
2025/06/18 12:44:12.744114 parser_service.go:138: 📦 [SAVE-QUESTION] HashKey: 3bd29cb801b86bfaf2eb23001bd7ca83
2025/06/18 12:44:12.744142 parser_service.go:139: 📦 [SAVE-QUESTION] Options数量: 4
2025/06/18 12:44:12.744168 solve_service.go:190: ✅ [OCR] 响应解析成功 - 题目类型: 单选题, 内容长度: 81
2025/06/18 12:44:12.744191 solve_service.go:194: 🔄 [CONTEXT] ProcessContext已更新
2025/06/18 12:44:12.744211 solve_service.go:195: 🔄 [CONTEXT] SaveQuestion.Type: 单选题
2025/06/18 12:44:12.744232 solve_service.go:196: 🔄 [CONTEXT] SaveQuestion.HashKey: 3bd29cb801b86bfaf2eb23001bd7ca83
2025/06/18 12:44:12.744252 solve_service.go:197: 🔄 [CONTEXT] SaveQuestion.QuestionLen: 24
2025/06/18 12:44:12.744273 solve_service.go:198: 🔄 [CONTEXT] SaveQuestion.UserURL: http://img.igmdns.com/images/cc0002.jpg
2025/06/18 12:44:12.744293 solve_service.go:199: 🔄 [CONTEXT] OCRToken: 1412
2025/06/18 12:44:12.744315 solve_service.go:200: 🔄 [CONTEXT] 当前处理阶段: OCR解析完成，准备进入缓存检查阶段
2025/06/18 12:44:12.744335 solve_service.go:87: ✅ [SOLVE] OCR处理成功 - Token消耗: 1412, 题目类型: 单选题
2025/06/18 12:44:12.744361 solve_service.go:90: 🔄 [SOLVE] 步骤4: 检查Redis缓存
2025/06/18 12:44:12.744381 solve_service.go:207: 🔍 [CACHE-CHECK] 开始检查Redis缓存
2025/06/18 12:44:12.744409 solve_service.go:208: 🔍 [CACHE-CHECK] 使用HashKey: 3bd29cb801b86bfaf2eb23001bd7ca83
2025/06/18 12:44:12.781588 solve_service.go:212: ⚪ [CACHE-CHECK] Redis缓存未命中 - Error: <nil>, Questions数量: 0
2025/06/18 12:44:12.781737 solve_service.go:213: 🔄 [CONTEXT] 缓存检查结果: 未命中，继续下一步
2025/06/18 12:44:12.781784 solve_service.go:95: ⚪ [SOLVE] Redis缓存未命中
2025/06/18 12:44:12.781824 solve_service.go:98: 🎯 [SOLVE] 步骤5: 检查MySQL精确匹配
2025/06/18 12:44:12.781858 solve_service.go:235: 🔍 [MYSQL-EXACT] 开始MySQL精确匹配
2025/06/18 12:44:12.781891 solve_service.go:236: 🔍 [MYSQL-EXACT] 使用HashKey: 3bd29cb801b86bfaf2eb23001bd7ca83

2025/06/18 12:44:12 [32m/Users/<USER>/Documents/Project/Solve/solve-go-api/internal/services/question_service.go:23
[0m[33m[59.930ms] [34;1m[rows:0][0m SELECT * FROM `hook_question_bank` WHERE hash_key = '3bd29cb801b86bfaf2eb23001bd7ca83'
2025/06/18 12:44:12.842031 solve_service.go:240: ⚪ [MYSQL-EXACT] MySQL精确匹配未命中 - Error: <nil>, Questions数量: 0
2025/06/18 12:44:12.842118 solve_service.go:241: 🔄 [CONTEXT] 精确匹配结果: 未命中，继续模糊匹配
2025/06/18 12:44:12.842169 solve_service.go:103: ⚪ [SOLVE] MySQL精确匹配未命中
2025/06/18 12:44:12.842219 solve_service.go:106: 🔍 [SOLVE] 步骤6: 检查MySQL模糊匹配
2025/06/18 12:44:12.842251 solve_service.go:273: 🔍 [MYSQL-FUZZY] 开始MySQL模糊匹配
2025/06/18 12:44:12.842279 solve_service.go:274: 🔍 [MYSQL-FUZZY] 题目类型: 单选题
2025/06/18 12:44:12.842305 solve_service.go:275: 🔍 [MYSQL-FUZZY] 清洗后内容: 如图所示驾驶机动车遇到这种情况时以下做法正确的是
2025/06/18 12:44:12.842330 solve_service.go:276: 🔍 [MYSQL-FUZZY] 题目长度: 24
2025/06/18 12:44:12.842352 match_service.go:30: 🔍 [FUZZY-MATCH] 开始三步筛选匹配

2025/06/18 12:44:12 [32m/Users/<USER>/Documents/Project/Solve/solve-go-api/internal/services/match_service.go:41
[0m[33m[60.356ms] [34;1m[rows:0][0m SELECT * FROM `hook_question_bank` WHERE verified = true AND type = 2 AND ABS(question_len - 24) <= 5
2025/06/18 12:44:12.902859 match_service.go:47: 🔍 [STEP-1] MySQL筛选完成 - 找到0个候选题目
2025/06/18 12:44:12.902927 solve_service.go:280: ⚪ [MYSQL-FUZZY] MySQL模糊匹配未命中 - Error: <nil>, Questions数量: 0
2025/06/18 12:44:12.902961 solve_service.go:281: 🔄 [CONTEXT] 模糊匹配结果: 未命中，需要调用AI模型
2025/06/18 12:44:12.902980 solve_service.go:111: ⚪ [SOLVE] MySQL模糊匹配未命中
2025/06/18 12:44:12.902997 solve_service.go:114: 🤖 [SOLVE] 步骤7: 调用Solve模型解答

2025/06/18 12:44:12 [32m/Users/<USER>/Documents/Project/Solve/solve-go-api/internal/services/model_service.go:39
[0m[33m[58.987ms] [34;1m[rows:1][0m SELECT * FROM `hook_models` WHERE model_name = 'qwen-plus' ORDER BY `hook_models`.`id` LIMIT 1
2025/06/18 12:44:12.962244 model_service.go:217: 🔍 [HTTP-REQUEST] 模型: qwen-plus
2025/06/18 12:44:12.962341 model_service.go:218: 🔍 [HTTP-REQUEST] 请求URL: https://dashscope.aliyuncs.com/api/v1/services/aigc/text-generation/generation
2025/06/18 12:44:12.962382 model_service.go:219: 🔍 [HTTP-REQUEST] 请求体长度: 814 bytes
2025/06/18 12:44:12.962442 model_service.go:224: 🔍 [HTTP-REQUEST] 完整请求体:
{
  "input": {
    "messages": [
      {
        "content": "严格标准返回json格式。示例{\"answer\":{\"所有正确答案\"}}\"analysis\":\"答案解析\",",
        "role": "system"
      },
      {
        "content": "权威解答问题,严格按照要求输出的完整json,其中的answer字段的格式必须正确,正确答案字母为键，正确选项答案内容为值\n\n题目内容：题目类型：单选题\n题目内容：如图所示，驾驶机动车遇到这种情况时，以下做法正确的是？\n选项：B:可随意通行 C:应停车察明水情确认安全后快速通过 D:应停车察明水情确认安全后低速通过 A:应减速观察水情然后加速行驶通过",
        "role": "user"
      }
    ]
  },
  "model": "qwen-plus",
  "parameters": {
    "presence_penalty": 1,
    "repetition_penalty": 1,
    "result_format": "json_object",
    "temperature": 0.1,
    "top_k": 1,
    "top_p": 0.01
  }
}
2025/06/18 12:44:12.962516 model_service.go:243: 🔍 [HTTP-REQUEST] 请求头 Content-Type: application/json
2025/06/18 12:44:12.962559 model_service.go:244: 🔍 [HTTP-REQUEST] 请求头 Authorization: Bearer sk-21d8cdf***
2025/06/18 12:44:17.962060 model_service.go:265: 🔍 [HTTP-RESPONSE] 响应状态码: 200
2025/06/18 12:44:17.962203 model_service.go:266: 🔍 [HTTP-RESPONSE] 响应体长度: 589 bytes
2025/06/18 12:44:17.962255 model_service.go:267: 🔍 [HTTP-RESPONSE] 响应头 Content-Type: application/json
2025/06/18 12:44:17.962321 model_service.go:272: 🔍 [HTTP-RESPONSE] 完整响应体:
{
  "output": {
    "finish_reason": "stop",
    "text": "```json\n{\n  \"answer\": {\n    \"D\": \"应停车察明水情确认安全后低速通过\"\n  },\n  \"analysis\": \"在遇到积水路段时，驾驶员应当首先停车观察，确保水情安全后，选择低速通过。这是为了防止因水深不明导致车辆受损或发生危险。选项D是正确做法，而其他选项均不符合安全驾驶的要求。\"\n}\n```"
  },
  "usage": {
    "total_tokens": 229,
    "output_tokens": 89,
    "input_tokens": 140,
    "prompt_tokens_details": {
      "cached_tokens": 0
    }
  },
  "request_id": "f0c28494-8b60-9c0b-af9c-a2d9c207c985"
}
2025/06/18 12:44:17.962402 model_service.go:284: ✅ [HTTP-RESPONSE] JSON解析成功
2025/06/18 12:44:17.962453 model_service.go:288: 🔍 [HTTP-DEBUG] Choices为空，尝试处理特殊模型格式
2025/06/18 12:44:17.962542 model_service.go:324: 🔍 [HTTP-DEBUG] 从Qwen output.text提取内容: ```json
{
  "answer": {
    "D": "应停车察明水情确认安全后低速通过"
  },
  "analysis
2025/06/18 12:44:17.962590 model_service.go:340: 🔍 [HTTP-DEBUG] Qwen格式转换完成
2025/06/18 12:44:17.962633 model_service.go:162: 🔍 [SOLVE-DEBUG] 原始响应Choices数量: 1
2025/06/18 12:44:17.962667 model_service.go:163: 🔍 [SOLVE-DEBUG] 原始响应Usage: {TotalTokens:229}
2025/06/18 12:44:17.962711 model_service.go:165: 🔍 [SOLVE-DEBUG] 第一个Choice内容类型: string
2025/06/18 12:44:17.962739 model_service.go:166: 🔍 [SOLVE-DEBUG] 第一个Choice内容: ```json
{
  "answer": {
    "D": "应停车察明水情确认安全后低速通过"
  },
  "analysis": "在遇到积水路段时，驾驶员应当首先停车观察，确保水情安全后，选择低速通过。这是为了防止因水深不明导致车辆受损或发生危险。选项D是正确做法，而其他选项均不符合安全驾驶的要求。"
}
```
2025/06/18 12:44:17.962775 parser_service.go:151: 🔍 [SOLVE-PARSE] 原始响应内容: ```json
{
  "answer": {
    "D": "应停车察明水情确认安全后低速通过"
  },
  "analysis": "在遇到积水路段时，驾驶员应当首先停车观察，确保水情安全后，选择低速通过。这是为了防止因水深不明导致车辆受损或发生危险。选项D是正确做法，而其他选项均不符合安全驾驶的要求。"
}
```
2025/06/18 12:44:17.962805 parser_service.go:297: 🔍 [JSON-EXTRACT] 开始提取JSON，原始内容: ```json
{
  "answer": {
    "D": "应停车察明水情确认安全后低速通过"
  },
  "analysis": "在遇到积水路段时，驾驶员应当首先停车观察，确保水情安全后，选择低速通过。这是为了防止因水深不明导致车辆受损或发生危险。选项D是正确做法，而其他选项均不符合安全驾驶的要求。"
}
```
2025/06/18 12:44:17.962833 parser_service.go:308: 🔍 [JSON-EXTRACT] 从```json```中提取: {
  "answer": {
    "D": "应停车察明水情确认安全后低速通过"
  },
  "analysis": "在遇到积水路段时，驾驶员应当首先停车观察，确保水情安全后，选择低速通过。这是为了防止因水深不明导致车辆受损或发生危险。选项D是正确做法，而其他选项均不符合安全驾驶的要求。"
}
2025/06/18 12:44:17.962860 parser_service.go:155: 🔍 [SOLVE-PARSE] 提取的JSON内容: {
  "answer": {
    "D": "应停车察明水情确认安全后低速通过"
  },
  "analysis": "在遇到积水路段时，驾驶员应当首先停车观察，确保水情安全后，选择低速通过。这是为了防止因水深不明导致车辆受损或发生危险。选项D是正确做法，而其他选项均不符合安全驾驶的要求。"
}
2025/06/18 12:44:17.962929 parser_service.go:163: 🔍 [SOLVE-PARSE] 解析成功 - Answer: map[D:应停车察明水情确认安全后低速通过], Analysis: 在遇到积水路段时，驾驶员应当首先停车观察，确保水情安全后，选择低速通过。这是为了防止因水深不明导致车辆受损或发生危险。选项D是正确做法，而其他选项均不符合安全驾驶的要求。
2025/06/18 12:44:17.962978 solve_service.go:120: ✅ [SOLVE] Solve模型处理成功
2025/06/18 12:44:17.963012 solve_service.go:123: 💾 [SOLVE] 步骤8: 保存题目到数据库并回写缓存

2025/06/18 12:44:18 [32m/Users/<USER>/Documents/Project/Solve/solve-go-api/internal/services/question_service.go:57
[0m[33m[128.501ms] [34;1m[rows:1][0m INSERT INTO `hook_question_bank` (`hash_key`,`type`,`content`,`content_clean`,`options`,`answer`,`analysis`,`image_url`,`user_url`,`verified`,`question_len`,`created_at`,`updated_at`) VALUES ('3bd29cb801b86bfaf2eb23001bd7ca83',2,'如图所示，驾驶机动车遇到这种情况时，以下做法正确的是？','如图所示驾驶机动车遇到这种情况时以下做法正确的是','{"A":"应减速观察水情然后加速行驶通过","B":"可随意通行","C":"应停车察明水情确认安全后快速通过","D":"应停车察明水情确认安全后低速通过"}','{"D":"应停车察明水情确认安全后低速通过"}','在遇到积水路段时，驾驶员应当首先停车观察，确保水情安全后，选择低速通过。这是为了防止因水深不明导致车辆受损或发生危险。选项D是正确做法，而其他选项均不符合安全驾驶的要求。','','http://img.igmdns.com/images/cc0002.jpg',false,24,'2025-06-18 12:44:17.992','2025-06-18 12:44:17.992')

2025/06/18 12:44:18 [32m/Users/<USER>/Documents/Project/Solve/solve-go-api/internal/services/question_service.go:23
[0m[33m[60.083ms] [34;1m[rows:1][0m SELECT * FROM `hook_question_bank` WHERE hash_key = '3bd29cb801b86bfaf2eb23001bd7ca83'
2025/06/18 12:44:18.191345 solve_service.go:129: ✅ [SOLVE] 保存和缓存成功
2025/06/18 12:44:18.191425 solve_service.go:131: 🎉 [SOLVE] 解题请求处理完成 - 总耗时: 8.429554708s
2025/06/18 12:44:18.191464 solve_handler.go:74: 📝 [HANDLER] 记录解题日志

2025/06/18 12:44:18 [32m/Users/<USER>/Documents/Project/Solve/solve-go-api/internal/services/log_service.go:41
[0m[33m[129.826ms] [34;1m[rows:1][0m INSERT INTO `hook_solve_logs` (`app_id`,`user_url`,`matched_id`,`ocr_token`,`source`,`data`,`status`,`latency`,`created_at`) VALUES ('NdOCNqqWPtrLFvZj','http://img.igmdns.com/images/cc0002.jpg',5,1412,'ai','[{"id":5,"type":"单选题","content":"如图所示，驾驶机动车遇到这种情况时，以下做法正确的是？","options":{"A":"应减速观察水情然后加速行驶通过","B":"可随意通行","C":"应停车察明水情确认安全后快速通过","D":"应停车察明水情确认安全后低速通过"},"answer":{"D":"应停车察明水情确认安全后低速通过"},"analysis":"在遇到积水路段时，驾驶员应当首先停车观察，确保水情安全后，选择低速通过。这是为了防止因水深不明导致车辆受损或发生危险。选项D是正确做法，而其他选项均不符合安全驾驶的要求。","image_url":"","user_url":"http://img.igmdns.com/images/cc0002.jpg"}]',1,8429,'2025-06-18 12:44:18.221')
2025/06/18 12:44:18.321445 solve_handler.go:83: 🎉 [HANDLER] 解题处理成功

2025/06/18 12:44:18 [32m/Users/<USER>/Documents/Project/Solve/solve-go-api/internal/services/app_service.go:105
[0m[33m[130.009ms] [34;1m[rows:1][0m UPDATE `hook_apps` SET `total_calls`=total_calls + 1 WHERE app_id = 'NdOCNqqWPtrLFvZj'

2025/06/18 12:44:18 [32m/Users/<USER>/Documents/Project/Solve/solve-go-api/internal/services/app_service.go:123
[0m[33m[60.081ms] [34;1m[rows:1][0m UPDATE `hook_user` SET `balance`=balance - 2824,`updated_at`='2025-06-18 12:44:18.482' WHERE id = 20

2025/06/18 12:44:18 [32m/Users/<USER>/Documents/Project/Solve/solve-go-api/internal/services/app_service.go:136
[0m[33m[59.688ms] [34;1m[rows:1][0m INSERT INTO `hook_balance_logs` (`user_id`,`change_amount`,`reason`,`operator_id`,`created_at`) VALUES (20,-2824,'API调用扣费',0,'2025-06-18 12:44:18.542')
time="2025-06-18T12:44:18+08:00" level=info msg="HTTP Request" body_size=760 client_ip="::1" error= latency=9.007735959s method=POST path=/api/v1/solve/question status_code=200 timestamp="2025-06-18T12:44:18+08:00" user_agent=PostmanRuntime-ApipostRuntime/1.1.0
[GIN] 2025/06/18 - 12:44:18 | 200 |    9.0079345s |             ::1 | POST     "/api/v1/solve/question"
